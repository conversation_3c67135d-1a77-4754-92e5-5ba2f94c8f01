# Complete Migration Master Checklist

**Project:** Virtualization Infrastructure Migration  
**Phases:** VM Migration + P2V Migration  
**Target:** Full Proxmox VE deployment with all workloads virtualized  
**Goes in:** 1.Projects - active infrastructure optimization project

## Migration Overview

### Current Status ✅
- [x] **File Reorganization Complete:** 214.11 GB of VM files organized
- [x] **VM Exports Complete:** All 6 VMs safely backed up
- [x] **PARA Structure Created:** Organized storage on Z: drive
- [x] **Documentation Complete:** Implementation guides ready

### Final Architecture Goal
```
Intel i9-10900 Physical Hardware (64GB RAM, 4x SSDs)
└── Proxmox VE (Bare Metal Hypervisor)
    ├── debian-dev-01 (VM ID: 101) - Development
    ├── rhino-dev-01 (VM ID: 102) - Development  
    ├── ws2025-lan-01 (VM ID: 103) - Development
    ├── gitlab-prod-01 (VM ID: 201) - Production
    ├── elara-prod-01 (VM ID: 202) - Production
    ├── ws2025-mgmt-01 (VM ID: 203) - Management
    └── windows-server-2025 (VM ID: 301) - P2V Host
```

## Phase 1: VM Migration to Proxmox

### Pre-Migration Checklist
- [ ] **Hardware Verified:** Intel i9-10900 VT-x support confirmed
- [ ] **Storage Planned:** SSD allocation for Proxmox installation
- [ ] **Network Planned:** IP addressing and VLAN configuration
- [ ] **Proxmox ISO Downloaded:** Latest Proxmox VE 8.x ISO ready
- [ ] **VirtIO Drivers Downloaded:** Windows VirtIO ISO available

### Critical Pre-Conversion Steps
- [ ] **Merge Elara Differencing Disk:** 
  ```powershell
  Edit-VHD -Path "HV-LAN-WS2025-01_903288FE-967F-45E3-9892-122EFC5C67AE.avhdx" -ParentPath "HV-LAN-WS2025-01.vhdx"
  ```
- [ ] **Merge HV-LAN-WS2025 Differencing Disk:**
  ```powershell
  Edit-VHD -Path "HV-LAN-WS2025-01_FEC81DD3-AC4C-4229-A17E-45D9339607C8.avhdx" -ParentPath "HV-LAN-WS2025-01.vhdx"
  ```
- [ ] **Verify Merges Successful:** Confirm disks are no longer differencing type
- [ ] **Test Merged Disks:** Verify integrity with Get-VHD command

### Proxmox Installation
- [ ] **Install Proxmox VE:** Clean installation on fastest SSD
- [ ] **Configure Storage Pools:** Set up VM and backup storage
- [ ] **Network Configuration:** Configure bridges and VLANs
- [ ] **System Updates:** Apply latest updates and patches
- [ ] **Install Tools:** qemu-img and other required utilities

### VM Conversion Process

#### Development VMs (Test First)
- [ ] **debian-dev-01 (6.41 GB):**
  - [ ] Convert: `qemu-img convert -p -O qcow2 Debian01.vhdx debian-dev-01-disk0.qcow2`
  - [ ] Create VM: `qm create 101 --name "debian-dev-01" --memory 2048 --cores 2`
  - [ ] Import disk: `qm disk import 101 debian-dev-01-disk0.qcow2 local-lvm`
  - [ ] Test boot and functionality

- [ ] **rhino-dev-01 (11.91 GB):**
  - [ ] Convert VHDX to qcow2
  - [ ] Create VM with 16GB RAM, 10 cores
  - [ ] Import and test

- [ ] **ws2025-lan-01 (17.94 GB):**
  - [ ] Convert VHDX to qcow2
  - [ ] Create Windows VM with VirtIO drivers
  - [ ] Test boot and network connectivity

#### Production VMs (After Development Testing)
- [ ] **gitlab-prod-01 (22.01 GB):**
  - [ ] Convert main disk and Q4OS disk
  - [ ] Create VM with 8GB RAM, 10 cores
  - [ ] Test GitLab functionality

- [ ] **elara-prod-01 (116.28 GB):**
  - [ ] Convert merged disk (will take significant time)
  - [ ] Create VM with appropriate resources
  - [ ] Extensive testing required

- [ ] **ws2025-mgmt-01 (39.56 GB):**
  - [ ] Convert merged disk
  - [ ] Create Windows VM with management tools
  - [ ] Test all management functions

### Post-Migration Validation
- [ ] **All VMs Boot Successfully:** Verify each VM starts without errors
- [ ] **Network Connectivity:** Test internal and external network access
- [ ] **Service Functionality:** Verify all applications and services work
- [ ] **Performance Testing:** Confirm acceptable performance levels
- [ ] **VirtIO Optimization:** Install VirtIO drivers for Windows VMs
- [ ] **Guest Agent Installation:** Install QEMU guest agent on all VMs
- [ ] **Backup Configuration:** Set up automated backup schedules

### Phase 1 Success Criteria
- [ ] All 6 VMs running natively on Proxmox
- [ ] No nested virtualization
- [ ] All services functional
- [ ] Network configurations updated
- [ ] Backup procedures operational
- [ ] Performance acceptable or improved

## Phase 2: P2V Migration of Windows Server Host

### Pre-P2V Preparation
- [ ] **Phase 1 Complete:** All VMs successfully migrated and validated
- [ ] **System Cleanup:** Remove Hyper-V role and clean D: drive
- [ ] **Service Optimization:** Disable unnecessary services
- [ ] **System Updates:** Apply all Windows updates
- [ ] **Configuration Documentation:** Export current system settings

### P2V Image Creation
- [ ] **Download Disk2VHD:** Microsoft Sysinternals tool ready
- [ ] **Create P2V Image:** 
  ```powershell
  .\disk2vhd.exe -accepteula C: "Z:\Hyper-V\3_RESOURCES\p2v\windows-server-2025-p2v.vhdx"
  ```
- [ ] **Verify Image Integrity:** Check VHDX file with Get-VHD
- [ ] **Transfer to Proxmox:** Copy VHDX to Proxmox host

### P2V Conversion and Import
- [ ] **Convert to qcow2:** 
  ```bash
  qemu-img convert -p -O qcow2 windows-server-2025-p2v.vhdx windows-server-2025.qcow2
  ```
- [ ] **Create VM:** Configure with 16GB RAM, 8 cores, UEFI boot
- [ ] **Import Disk:** Import P2V disk as IDE initially
- [ ] **First Boot Test:** Verify Windows boots successfully

### Post-P2V Configuration
- [ ] **Boot Repair:** Fix any boot issues with Windows Recovery
- [ ] **VirtIO Driver Installation:** Install network and storage drivers
- [ ] **Switch to VirtIO SCSI:** Change from IDE to VirtIO for performance
- [ ] **Network Reconfiguration:** Update network settings for new environment
- [ ] **Guest Agent Installation:** Install QEMU guest agent
- [ ] **Service Validation:** Verify all Windows services function correctly

### Final System Validation
- [ ] **All 7 VMs Operational:** 6 migrated VMs + 1 P2V Windows Server
- [ ] **Resource Optimization:** Adjust CPU, memory, and storage allocations
- [ ] **Network Optimization:** Configure VLANs and firewall rules
- [ ] **Backup Integration:** Include P2V VM in backup schedules
- [ ] **Performance Monitoring:** Set up monitoring and alerting
- [ ] **Documentation Updates:** Update all system documentation

## Safety and Rollback Procedures

### Emergency Rollback Options
- [ ] **Individual VM Rollback:** Restore from Hyper-V exports
- [ ] **Complete System Rollback:** Reinstall Windows Server 2025 physically
- [ ] **Partial Rollback:** Keep some VMs on Proxmox, others on physical

### Safety Checkpoints
- [ ] **After Differencing Disk Merge:** Verify no data corruption
- [ ] **After First VM Migration:** Test process with development VM
- [ ] **After Each VM Migration:** Validate functionality before proceeding
- [ ] **After Phase 1 Complete:** Full system validation before P2V
- [ ] **After P2V Creation:** Verify image integrity before conversion
- [ ] **After Final Migration:** Complete system testing and validation

## Resource Allocation Guidelines

### Recommended VM Configurations
| VM Name | VM ID | RAM (GB) | CPU Cores | Storage | Priority |
|---------|-------|----------|-----------|---------|----------|
| debian-dev-01 | 101 | 2 | 2 | SSD | Low |
| rhino-dev-01 | 102 | 16 | 10 | SSD | Medium |
| ws2025-lan-01 | 103 | 4 | 10 | SSD | Low |
| gitlab-prod-01 | 201 | 8 | 10 | NVMe | High |
| elara-prod-01 | 202 | 8 | 10 | NVMe | High |
| ws2025-mgmt-01 | 203 | 8 | 10 | SSD | High |
| windows-server-2025 | 301 | 16 | 8 | SSD | Medium |

### Total Resource Usage
- **Total RAM Allocated:** 62 GB (of 64 GB available)
- **Total CPU Cores:** 60 vCPUs (of 20 logical processors)
- **Storage Requirements:** ~300 GB for all VMs

## Timeline Estimates

### Phase 1: VM Migration (3-5 days)
- **Day 1:** Proxmox installation and configuration
- **Day 2:** Differencing disk merge and first VM conversions
- **Day 3:** Development VM migrations and testing
- **Day 4:** Production VM migrations
- **Day 5:** Validation, optimization, and backup configuration

### Phase 2: P2V Migration (2-3 days)
- **Day 1:** P2V preparation and image creation
- **Day 2:** P2V conversion and initial VM setup
- **Day 3:** Configuration, testing, and final validation

### Total Project Timeline: 5-8 days

## Success Metrics

### Technical Metrics
- [ ] **100% VM Migration Success:** All 6 VMs operational on Proxmox
- [ ] **Zero Data Loss:** All data preserved during migration
- [ ] **Performance Maintained:** No significant performance degradation
- [ ] **Network Connectivity:** All network services functional
- [ ] **Backup Success:** All VMs included in backup schedules

### Operational Metrics
- [ ] **Simplified Management:** Single Proxmox interface for all VMs
- [ ] **Improved Resource Utilization:** Better CPU and memory efficiency
- [ ] **Enhanced Backup/Recovery:** Automated backup procedures
- [ ] **Reduced Hardware Dependencies:** All workloads virtualized
- [ ] **Future Scalability:** Platform ready for expansion

## Final Deliverables

### Documentation
- [x] **Phase 1 Implementation Guide:** Detailed VM migration procedures
- [x] **Phase 2 Implementation Guide:** P2V migration procedures
- [x] **Master Checklist:** This comprehensive checklist
- [ ] **Final System Documentation:** Updated after migration completion
- [ ] **Lessons Learned Document:** Post-migration analysis and improvements

### System Configuration
- [ ] **Proxmox VE Environment:** Fully configured and operational
- [ ] **All VMs Migrated:** 6 original VMs + 1 P2V Windows Server
- [ ] **Backup Procedures:** Automated backup schedules configured
- [ ] **Monitoring Setup:** Performance monitoring and alerting
- [ ] **Network Configuration:** Optimized network setup with VLANs

---

**Project Status:** Ready for Phase 1 implementation  
**Critical Success Factor:** Proper handling of differencing disks  
**Next Action:** Begin Proxmox VE installation and differencing disk merge
