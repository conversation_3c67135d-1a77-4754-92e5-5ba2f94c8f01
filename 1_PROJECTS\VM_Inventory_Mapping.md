# VM Inventory and Migration Mapping Document

**Date:** 2025-07-18  
**Project:** Hyper-V File Reorganization  
**Goes in:** 1.Projects - active infrastructure optimization project

## Current VM Inventory (Before Migration)

### VM Configuration Summary
| Original Name | CPU Cores | RAM (GB) | Disk Size (GB) | Dynamic Memory | Status |
|---------------|-----------|----------|----------------|----------------|--------|
| Debian01 | 2 | 2 | 127 | Yes | Development |
| Elara | 10 | 8 | 100 | Yes | Production |
| Gitlab | 10 | 8 | 30 | Yes | Production |
| HV-LAN-WS2025 | 10 | 8 | 100 | Yes | Management |
| LAN-HV-WS-2025-01 | 10 | 4 | 100 | No | Development |
| rightrhino | 10 | 16 | 60 | Yes | Development |

### Current File Locations (Drive D)

#### Debian01
- **Config Path:** `D:\Hyper-V-Config-Files\Debian01\`
- **Disk Path:** `D:\Hyper-V-Drives\Debian01.vhdx`
- **Disk Type:** Dynamic VHDX
- **Dependencies:** None identified
- **Network:** Default Switch

#### Elara
- **Config Path:** `D:\Hyper-V-Config-Files\Elara\`
- **Disk Path:** `D:\HV\Drives\105\HV-LAN-WS2025-01_903288FE-967F-45E3-9892-122EFC5C67AE.avhdx`
- **Disk Type:** Differencing VHDX
- **Dependencies:** Parent disk relationship
- **Network:** Default Switch

#### Gitlab
- **Config Path:** `D:\HV\Configs\Gitlab\`
- **Disk Path:** `D:\HV\Configs\Gitlab\Virtual Hard Disks\Gitlab.vhdx`
- **Disk Type:** Dynamic VHDX
- **Dependencies:** Network services, repositories
- **Network:** Default Switch

#### HV-LAN-WS2025
- **Config Path:** `D:\Hyper-V-Config-Files\HV-LAN-WS2025\`
- **Disk Path:** `D:\Hyper-V-Drives\HV-LAN-WS2025\HV-LAN-WS2025-01_FEC81DD3-AC4C-4229-A17E-45D9339607C8.avhdx`
- **Disk Type:** Differencing VHDX
- **Dependencies:** Parent disk relationship
- **Network:** Default Switch

#### LAN-HV-WS-2025-01
- **Config Path:** `D:\Hyper-V-Config-Files\LAN-HV-WS-2025-01\`
- **Disk Path:** `D:\Hyper-V-Drives\LAN-HV-WS-2025-01.vhdx`
- **Disk Type:** Dynamic VHDX
- **Dependencies:** None identified
- **Network:** Default Switch

#### rightrhino
- **Config Path:** `D:\HV\Configs\rightrhino\`
- **Disk Path:** `D:\HV\Configs\rightrhino\Virtual Hard Disks\rightrhino.vhdx`
- **Disk Type:** Dynamic VHDX
- **Dependencies:** None identified
- **Network:** Default Switch

## Proposed New Structure (Drive Z)

### Naming Convention Rules
- **Format:** `[purpose]-[environment]-[sequence]`
- **Purpose:** gitlab, debian, ws2025, rhino, elara
- **Environment:** prod, dev, mgmt, test
- **Sequence:** 01, 02, 03, etc.

### Migration Mapping Table

| Original Name | New Standardized Name | Category | New Location |
|---------------|----------------------|----------|--------------|
| Debian01 | debian-dev-01 | Development | `Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01\` |
| Elara | elara-prod-01 | Production | `Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\` |
| Gitlab | gitlab-prod-01 | Production | `Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01\` |
| HV-LAN-WS2025 | ws2025-mgmt-01 | Management | `Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\` |
| LAN-HV-WS-2025-01 | ws2025-lan-01 | Development | `Z:\Hyper-V\1_PROJECTS\development-vms\ws2025-lan-01\` |
| rightrhino | rhino-dev-01 | Development | `Z:\Hyper-V\1_PROJECTS\development-vms\rhino-dev-01\` |

### Detailed New File Paths

#### debian-dev-01 (formerly Debian01)
- **New Config Path:** `Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01\config\`
- **New Disk Path:** `Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01\disks\debian-dev-01.vhdx`
- **Backup Location:** `Z:\Hyper-V\4_ARCHIVES\original-exports\Debian01\`

#### elara-prod-01 (formerly Elara)
- **New Config Path:** `Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\config\`
- **New Disk Path:** `Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\disks\elara-prod-01.avhdx`
- **Backup Location:** `Z:\Hyper-V\4_ARCHIVES\original-exports\Elara\`
- **Special Note:** Differencing disk - parent relationship must be maintained

#### gitlab-prod-01 (formerly Gitlab)
- **New Config Path:** `Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01\config\`
- **New Disk Path:** `Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01\disks\gitlab-prod-01.vhdx`
- **Backup Location:** `Z:\Hyper-V\4_ARCHIVES\original-exports\Gitlab\`

#### ws2025-mgmt-01 (formerly HV-LAN-WS2025)
- **New Config Path:** `Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\config\`
- **New Disk Path:** `Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\disks\ws2025-mgmt-01.avhdx`
- **Backup Location:** `Z:\Hyper-V\4_ARCHIVES\original-exports\HV-LAN-WS2025\`
- **Special Note:** Differencing disk - parent relationship must be maintained

#### ws2025-lan-01 (formerly LAN-HV-WS-2025-01)
- **New Config Path:** `Z:\Hyper-V\1_PROJECTS\development-vms\ws2025-lan-01\config\`
- **New Disk Path:** `Z:\Hyper-V\1_PROJECTS\development-vms\ws2025-lan-01\disks\ws2025-lan-01.vhdx`
- **Backup Location:** `Z:\Hyper-V\4_ARCHIVES\original-exports\LAN-HV-WS-2025-01\`

#### rhino-dev-01 (formerly rightrhino)
- **New Config Path:** `Z:\Hyper-V\1_PROJECTS\development-vms\rhino-dev-01\config\`
- **New Disk Path:** `Z:\Hyper-V\1_PROJECTS\development-vms\rhino-dev-01\disks\rhino-dev-01.vhdx`
- **Backup Location:** `Z:\Hyper-V\4_ARCHIVES\original-exports\rightrhino\`

## Migration Validation Checklist

### Pre-Migration Verification
- [ ] All VMs are powered off
- [ ] Current VM configurations documented
- [ ] Network settings recorded
- [ ] Disk relationships mapped (especially differencing disks)
- [ ] Available space on Z: drive confirmed (870GB available, ~350GB needed)

### During Migration Tracking
- [ ] File copy operations completed successfully
- [ ] No corruption detected during copy
- [ ] File sizes match between source and destination
- [ ] Timestamps preserved correctly
- [ ] All subdirectories and files copied

### Post-Migration Validation
- [ ] Each VM boots successfully from new location
- [ ] Network connectivity maintained
- [ ] All services start correctly
- [ ] Performance comparable to original
- [ ] No data loss detected

### VM-Specific Validation Tests

#### debian-dev-01
- [ ] SSH access working
- [ ] All installed packages functional
- [ ] Network services responding
- [ ] File system integrity check passed

#### elara-prod-01
- [ ] Application services start correctly
- [ ] Database connectivity maintained
- [ ] User access preserved
- [ ] Performance metrics normal

#### gitlab-prod-01
- [ ] GitLab web interface accessible
- [ ] Repository data intact
- [ ] CI/CD pipelines functional
- [ ] User authentication working

#### ws2025-mgmt-01
- [ ] Windows services start correctly
- [ ] Domain connectivity maintained
- [ ] Management tools functional
- [ ] Network shares accessible

#### ws2025-lan-01
- [ ] Windows boot successful
- [ ] Network configuration preserved
- [ ] Applications functional
- [ ] User profiles intact

#### rhino-dev-01
- [ ] Development environment functional
- [ ] All tools and applications working
- [ ] Network connectivity maintained
- [ ] File system integrity verified

## Rollback Procedures

### If Individual VM Issues
1. Stop problematic VM
2. Remove from Hyper-V Manager
3. Import from backup in `Z:\Hyper-V\4_ARCHIVES\original-exports\`
4. Restore original configuration
5. Test functionality

### If System-Wide Issues
1. Stop all migrated VMs
2. Remove all new VMs from Hyper-V Manager
3. Restore original VM registrations
4. Point Hyper-V back to D: drive locations
5. Verify all original VMs functional

### Emergency Contacts
- **Primary:** Infrastructure team lead
- **Secondary:** System administrator
- **Escalation:** IT management

## Success Criteria

### Technical Success
- [ ] All VMs operational from Z: drive
- [ ] Performance maintained or improved
- [ ] No data loss or corruption
- [ ] Consistent naming convention implemented
- [ ] PARA structure properly organized

### Operational Success
- [ ] Reduced management complexity
- [ ] Improved backup procedures
- [ ] Better resource utilization
- [ ] Clear documentation maintained
- [ ] Team trained on new structure

---

**Document Status:** Ready for implementation  
**Last Updated:** 2025-07-18  
**Next Review:** After each migration phase completion
