# Network Security Fundamentals for Beginners

**Goes in 3.Resources** - Essential network security concepts and practices for infrastructure management beginners.

## What is Network Security?

Network security is like having locks, alarms, and security guards for your digital infrastructure. It protects your computers, data, and network connections from unauthorized access, attacks, and damage.

### Why Network Security Matters
- **Protect Data:** Keep sensitive information safe
- **Prevent Downtime:** Avoid service interruptions from attacks
- **Maintain Trust:** Protect your reputation and user confidence
- **Compliance:** Meet legal and regulatory requirements
- **Cost Savings:** Prevent expensive security incidents

## Core Security Concepts

### 1. Defense in Depth
Using multiple layers of security controls, like having multiple locks on your house.

**Example Layers:**
- **Perimeter:** Firewall at network edge
- **Network:** Internal firewalls and segmentation
- **Host:** Antivirus and host-based firewalls
- **Application:** Secure coding and input validation
- **Data:** Encryption and access controls

### 2. Principle of Least Privilege
Give users and systems only the minimum access they need to do their job.

**Examples:**
- Regular users can't install software
- Database users can only access specific databases
- Network devices only allow necessary protocols

### 3. Zero Trust Model
"Never trust, always verify" - assume nothing is secure and verify everything.

**Key Elements:**
- Verify every user and device
- Use strong authentication
- Monitor all network traffic
- Limit access to resources

## Network Security Components

### 1. Firewalls
Digital barriers that control network traffic based on security rules.

**Types:**
- **Network Firewall:** Controls traffic between networks
- **Host Firewall:** Protects individual computers
- **Application Firewall:** Protects specific applications

**Basic Rules:**
- Block all traffic by default
- Allow only necessary services
- Log all blocked attempts
- Regularly review and update rules

### 2. Network Segmentation
Dividing your network into separate sections to limit damage from security breaches.

**Our Network Segments:**
- **Management (111.10.0/24):** Infrastructure management only
- **Servers (111.20.0/24):** Production servers
- **Virtualization (111.30.0/24):** VM and container traffic
- **DMZ (111.40.0/24):** External-facing services
- **Guest (111.50.0/24):** Isolated guest access

### 3. VPN (Virtual Private Network)
Creates secure, encrypted connections over the internet.

**Types:**
- **Site-to-Site:** Connects different locations
- **Remote Access:** Allows secure remote connections
- **Client-to-Site:** Individual users connecting to network

**Our VPN Setup:**
- **Proton VPN:** External privacy and security
- **WireGuard:** Site-to-site and remote access
- **Hetzner Access:** Secure connection to remote servers

### 4. DNS Security
Protecting domain name resolution from attacks and manipulation.

**Security Measures:**
- **DNS Filtering:** Block malicious domains
- **Private DNS:** Use trusted DNS servers
- **DNS over HTTPS:** Encrypt DNS queries
- **Local DNS:** Reduce external dependencies

## Common Network Threats

### 1. Malware
Malicious software designed to damage or gain unauthorized access.

**Types:**
- **Viruses:** Self-replicating programs
- **Trojans:** Disguised malicious programs
- **Ransomware:** Encrypts data for ransom
- **Spyware:** Steals information secretly

**Protection:**
- Keep software updated
- Use antivirus software
- Be cautious with downloads
- Regular system scans

### 2. Network Attacks
Attempts to compromise network infrastructure.

**Common Attacks:**
- **DDoS:** Overwhelming services with traffic
- **Man-in-the-Middle:** Intercepting communications
- **Port Scanning:** Looking for vulnerable services
- **Network Sniffing:** Capturing network traffic

**Protection:**
- Use firewalls and intrusion detection
- Encrypt network communications
- Monitor network traffic
- Keep systems patched

### 3. Social Engineering
Manipulating people to reveal information or perform actions.

**Common Techniques:**
- **Phishing:** Fake emails requesting information
- **Pretexting:** Creating false scenarios
- **Baiting:** Offering something to gain access
- **Tailgating:** Following authorized users

**Protection:**
- Security awareness training
- Verify requests through separate channels
- Be suspicious of unsolicited contacts
- Follow security procedures

## Implementing Network Security

### 1. Firewall Configuration
**Basic Firewall Rules for Our Network:**

```
# Allow management access from admin network
ALLOW **************/24 -> *************/24 port 22,443,3389

# Allow server communication
ALLOW **************/24 -> **************/24 port 80,443,3306

# Block guest network from internal resources
DENY **************/24 -> *************/19

# Allow internet access for all internal networks
ALLOW *************/24 -> 0.0.0.0/0 port 80,443

# Log and deny everything else
LOG DENY all
```

### 2. Network Monitoring
**What to Monitor:**
- Unusual traffic patterns
- Failed login attempts
- Unauthorized access attempts
- Bandwidth usage spikes
- New devices on network

**Tools:**
- **SIEM:** Security Information and Event Management
- **IDS/IPS:** Intrusion Detection/Prevention Systems
- **Network Scanners:** Regular vulnerability scans
- **Log Analysis:** Centralized log monitoring

### 3. Access Control
**Authentication Methods:**
- **Passwords:** Strong, unique passwords
- **Multi-Factor Authentication:** Something you know + something you have
- **Certificates:** Digital certificates for devices
- **Biometrics:** Fingerprints, facial recognition

**Authorization Levels:**
- **Read-Only:** View information only
- **User:** Standard user privileges
- **Administrator:** Full system access
- **Service Account:** Automated system access

## Security Best Practices

### 1. Password Security
- **Length:** Minimum 12 characters
- **Complexity:** Mix of letters, numbers, symbols
- **Uniqueness:** Different password for each system
- **Management:** Use password managers
- **Rotation:** Change passwords regularly

### 2. Software Updates
- **Operating Systems:** Install security patches promptly
- **Applications:** Keep all software current
- **Firmware:** Update router and device firmware
- **Antivirus:** Keep definitions updated
- **Testing:** Test updates in non-production first

### 3. Backup Security
- **Encryption:** Encrypt backup data
- **Offsite Storage:** Keep backups in separate location
- **Access Control:** Limit who can access backups
- **Testing:** Regularly test backup restoration
- **Versioning:** Keep multiple backup versions

### 4. Incident Response
**Preparation:**
1. Create incident response plan
2. Define roles and responsibilities
3. Establish communication procedures
4. Prepare recovery tools and procedures

**Response Steps:**
1. **Identify:** Detect and analyze the incident
2. **Contain:** Limit the scope and impact
3. **Eradicate:** Remove the threat
4. **Recover:** Restore normal operations
5. **Learn:** Document lessons learned

## Security Tools for Our Environment

### 1. OPNsense Firewall
- **Purpose:** Network traffic filtering and monitoring
- **Features:** Intrusion detection, VPN, traffic shaping
- **Management:** Web-based interface
- **Monitoring:** Real-time traffic analysis

### 2. DNS Filtering (Pi-hole)
- **Purpose:** Block malicious and unwanted domains
- **Features:** Ad blocking, malware protection
- **Management:** Web interface for configuration
- **Monitoring:** Query logs and statistics

### 3. VPN Services
- **WireGuard:** Fast, secure VPN protocol
- **OpenVPN:** Flexible, widely supported
- **IPSec:** Enterprise-grade VPN solution
- **Management:** Configuration and monitoring tools

### 4. Network Monitoring
- **PRTG:** Comprehensive network monitoring
- **Nagios:** Open-source monitoring solution
- **Zabbix:** Enterprise monitoring platform
- **Simple Tools:** ping, traceroute, netstat

## Creating a Security Policy

### 1. Access Policy
- Who can access what resources
- Authentication requirements
- Password policies
- Remote access procedures

### 2. Network Policy
- Allowed network protocols
- Firewall rule standards
- Network segmentation requirements
- Wireless network security

### 3. Incident Response Policy
- Incident classification levels
- Response procedures
- Communication requirements
- Recovery procedures

### 4. Change Management Policy
- How to request changes
- Testing requirements
- Approval processes
- Documentation standards

## Security Checklist for Beginners

### Daily Tasks
- [ ] Review security alerts and logs
- [ ] Check for failed login attempts
- [ ] Monitor network traffic patterns
- [ ] Verify backup completion

### Weekly Tasks
- [ ] Review firewall logs
- [ ] Check for software updates
- [ ] Analyze security reports
- [ ] Test security controls

### Monthly Tasks
- [ ] Review user access rights
- [ ] Update security documentation
- [ ] Conduct security awareness training
- [ ] Test incident response procedures

### Quarterly Tasks
- [ ] Comprehensive security audit
- [ ] Penetration testing
- [ ] Policy review and updates
- [ ] Disaster recovery testing

## Getting Help

### Internal Resources
- Document all security procedures
- Create emergency contact lists
- Maintain vendor support information
- Keep security tools updated

### External Resources
- **NIST Cybersecurity Framework:** Industry standards
- **SANS Institute:** Security training and resources
- **CVE Database:** Known vulnerabilities
- **Security Communities:** Forums and discussion groups

---

**Remember:** Security is not a one-time setup but an ongoing process. Start with basic protections and gradually improve your security posture as you learn and grow.

*Guide Version: 1.0*
*Last Updated: 2025-07-18*
*Intended Audience: Infrastructure Management Beginners*
