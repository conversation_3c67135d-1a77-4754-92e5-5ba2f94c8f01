# Hyper-V VM Migration Scripts
# Project: Safe VM File Reorganization
# Goes in: 3.Resources - reference materials and scripts
# Date: 2025-07-18

# IMPORTANT: Run each section separately and verify results before proceeding
# DO NOT run the entire script at once

#region Phase 1: Create Directory Structure
Write-Host "=== Phase 1: Creating PARA Directory Structure ===" -ForegroundColor Green

# Verify Z: drive space first
$zDrive = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "Z:"}
$freeSpaceGB = [math]::Round($zDrive.FreeSpace/1GB, 2)
Write-Host "Z: drive free space: $freeSpaceGB GB" -ForegroundColor Yellow

if ($freeSpaceGB -lt 400) {
    Write-Error "Insufficient space on Z: drive. Need at least 400GB, have $freeSpaceGB GB"
    exit 1
}

# Create PARA structure
$directories = @(
    "Z:\Hyper-V",
    "Z:\Hyper-V\1_PROJECTS\production-vms",
    "Z:\Hyper-V\1_PROJECTS\development-vms", 
    "Z:\Hyper-V\2_AREAS\templates",
    "Z:\Hyper-V\2_AREAS\iso-images",
    "Z:\Hyper-V\2_AREAS\shared-resources",
    "Z:\Hyper-V\3_RESOURCES\backups\pre-migration",
    "Z:\Hyper-V\3_RESOURCES\backups\daily",
    "Z:\Hyper-V\3_RESOURCES\documentation",
    "Z:\Hyper-V\3_RESOURCES\scripts",
    "Z:\Hyper-V\4_ARCHIVES\original-exports",
    "Z:\Hyper-V\4_ARCHIVES\original-d-drive",
    "Z:\Hyper-V\4_ARCHIVES\virtualbox-backup"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -Path $dir -ItemType Directory -Force
        Write-Host "Created: $dir" -ForegroundColor Green
    } else {
        Write-Host "Exists: $dir" -ForegroundColor Yellow
    }
}

Write-Host "Directory structure created successfully!" -ForegroundColor Green
#endregion

#region Phase 1: Safety Backup - Export All VMs
Write-Host "=== Phase 1: Exporting VMs for Safety Backup ===" -ForegroundColor Green

# Get all VMs and their status
$vms = Get-VM
Write-Host "Found $($vms.Count) VMs to export" -ForegroundColor Yellow

foreach ($vm in $vms) {
    Write-Host "Processing VM: $($vm.Name)" -ForegroundColor Cyan
    
    # Ensure VM is off
    if ($vm.State -ne "Off") {
        Write-Host "Stopping VM: $($vm.Name)" -ForegroundColor Yellow
        Stop-VM -Name $vm.Name -Force
        Start-Sleep -Seconds 10
    }
    
    # Export VM
    $exportPath = "Z:\Hyper-V\4_ARCHIVES\original-exports"
    try {
        Export-VM -Name $vm.Name -Path $exportPath
        Write-Host "Successfully exported: $($vm.Name)" -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to export $($vm.Name): $($_.Exception.Message)"
    }
}

Write-Host "VM exports completed!" -ForegroundColor Green
#endregion

#region Phase 1: Copy VM Files to New Structure
Write-Host "=== Phase 1: Copying VM Files to New Structure ===" -ForegroundColor Green

# Define copy operations
$copyOperations = @(
    @{
        Source = "D:\HV\Configs\Gitlab"
        Destination = "Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01"
        VMName = "Gitlab"
    },
    @{
        Source = "D:\HV\Drives\105"
        Destination = "Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\drives"
        VMName = "Elara"
    },
    @{
        Source = "D:\Hyper-V-Drives\HV-LAN-WS2025"
        Destination = "Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\drives"
        VMName = "HV-LAN-WS2025"
    },
    @{
        Source = "D:\Hyper-V-Drives\Debian01.vhdx"
        Destination = "Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01\disks\"
        VMName = "Debian01"
    },
    @{
        Source = "D:\HV\Configs\rightrhino"
        Destination = "Z:\Hyper-V\1_PROJECTS\development-vms\rhino-dev-01"
        VMName = "rightrhino"
    },
    @{
        Source = "D:\Hyper-V-Drives\LAN-HV-WS-2025-01.vhdx"
        Destination = "Z:\Hyper-V\1_PROJECTS\development-vms\ws2025-lan-01\disks\"
        VMName = "LAN-HV-WS-2025-01"
    }
)

foreach ($operation in $copyOperations) {
    Write-Host "Copying files for VM: $($operation.VMName)" -ForegroundColor Cyan
    Write-Host "From: $($operation.Source)" -ForegroundColor Gray
    Write-Host "To: $($operation.Destination)" -ForegroundColor Gray
    
    # Create destination directory if it doesn't exist
    if (!(Test-Path $operation.Destination)) {
        New-Item -Path $operation.Destination -ItemType Directory -Force
    }
    
    # Use robocopy for reliable copying
    if (Test-Path $operation.Source) {
        if ((Get-Item $operation.Source).PSIsContainer) {
            # Directory copy
            $result = robocopy $operation.Source $operation.Destination /E /COPY:DAT /R:3 /W:10 /NP
        } else {
            # File copy
            $result = robocopy (Split-Path $operation.Source) $operation.Destination (Split-Path $operation.Source -Leaf) /COPY:DAT /R:3 /W:10 /NP
        }
        
        if ($LASTEXITCODE -le 7) {
            Write-Host "Successfully copied: $($operation.VMName)" -ForegroundColor Green
        } else {
            Write-Error "Copy failed for $($operation.VMName) with exit code: $LASTEXITCODE"
        }
    } else {
        Write-Warning "Source path not found: $($operation.Source)"
    }
}

# Copy configuration files
Write-Host "Copying Hyper-V configuration files..." -ForegroundColor Cyan
robocopy "D:\Hyper-V-Config-Files" "Z:\Hyper-V\3_RESOURCES\documentation\original-configs" /E /COPY:DAT /R:3 /W:10 /NP

Write-Host "File copying completed!" -ForegroundColor Green
#endregion

#region Phase 2: VirtualBox Cleanup
Write-Host "=== Phase 2: VirtualBox Cleanup ===" -ForegroundColor Green

# First backup VirtualBox files
Write-Host "Backing up VirtualBox files..." -ForegroundColor Cyan
if (Test-Path "D:\VirtualBoxVMs") {
    robocopy "D:\VirtualBoxVMs" "Z:\Hyper-V\4_ARCHIVES\virtualbox-backup\VMs" /E /COPY:DAT /R:3 /W:10 /NP
    Write-Host "VirtualBox VMs backed up" -ForegroundColor Green
}

if (Test-Path "D:\VirtualBox") {
    robocopy "D:\VirtualBox" "Z:\Hyper-V\4_ARCHIVES\virtualbox-backup\Installation" /E /COPY:DAT /R:3 /W:10 /NP
    Write-Host "VirtualBox installation backed up" -ForegroundColor Green
}

# Uninstall VirtualBox
Write-Host "Uninstalling VirtualBox..." -ForegroundColor Yellow
$vboxProducts = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*VirtualBox*"}
foreach ($product in $vboxProducts) {
    Write-Host "Uninstalling: $($product.Name)" -ForegroundColor Yellow
    $product.Uninstall()
}

# Clean up directories (after backup)
Write-Host "Cleaning up VirtualBox directories..." -ForegroundColor Yellow
if (Test-Path "D:\VirtualBox") {
    Remove-Item "D:\VirtualBox" -Recurse -Force
    Write-Host "Removed D:\VirtualBox" -ForegroundColor Green
}

if (Test-Path "D:\VirtualBoxVMs") {
    Remove-Item "D:\VirtualBoxVMs" -Recurse -Force
    Write-Host "Removed D:\VirtualBoxVMs" -ForegroundColor Green
}

Write-Host "VirtualBox cleanup completed!" -ForegroundColor Green
#endregion

#region Phase 3: Validation Functions
function Test-VMFiles {
    param([string]$VMName, [string]$NewPath)
    
    Write-Host "Validating files for $VMName..." -ForegroundColor Cyan
    
    if (Test-Path $NewPath) {
        $files = Get-ChildItem $NewPath -Recurse -File
        Write-Host "Found $($files.Count) files in $NewPath" -ForegroundColor Green
        
        # Check for VHDX files
        $vhdxFiles = $files | Where-Object {$_.Extension -eq ".vhdx" -or $_.Extension -eq ".avhdx"}
        if ($vhdxFiles.Count -gt 0) {
            Write-Host "Found $($vhdxFiles.Count) disk files" -ForegroundColor Green
            foreach ($vhdx in $vhdxFiles) {
                Write-Host "  - $($vhdx.Name) ($([math]::Round($vhdx.Length/1GB, 2)) GB)" -ForegroundColor Gray
            }
        } else {
            Write-Warning "No VHDX files found for $VMName"
        }
        
        return $true
    } else {
        Write-Error "Path not found: $NewPath"
        return $false
    }
}

# Validate all copied files
Write-Host "=== Phase 3: Validating Copied Files ===" -ForegroundColor Green

$validationTests = @(
    @{Name = "gitlab-prod-01"; Path = "Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01"},
    @{Name = "elara-prod-01"; Path = "Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01"},
    @{Name = "ws2025-mgmt-01"; Path = "Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01"},
    @{Name = "debian-dev-01"; Path = "Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01"},
    @{Name = "rhino-dev-01"; Path = "Z:\Hyper-V\1_PROJECTS\development-vms\rhino-dev-01"},
    @{Name = "ws2025-lan-01"; Path = "Z:\Hyper-V\1_PROJECTS\development-vms\ws2025-lan-01"}
)

$allValid = $true
foreach ($test in $validationTests) {
    $result = Test-VMFiles -VMName $test.Name -NewPath $test.Path
    if (!$result) { $allValid = $false }
}

if ($allValid) {
    Write-Host "All file validations passed!" -ForegroundColor Green
} else {
    Write-Error "Some validations failed. Review before proceeding."
}
#endregion

Write-Host "=== Migration Scripts Completed ===" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review validation results above" -ForegroundColor White
Write-Host "2. Test VM imports manually before proceeding" -ForegroundColor White
Write-Host "3. Only proceed to cleanup after 100% validation" -ForegroundColor White
