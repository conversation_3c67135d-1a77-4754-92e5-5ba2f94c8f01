# Hyper-V File Reorganization Implementation Plan

**Project:** Safe VM File Migration and Organization  
**Target:** Move all VM files from Drive D to Drive Z with PARA structure  
**Timeline:** 2-3 days with extensive testing  
**Goes in:** 1.Projects - active infrastructure optimization project

## Current VM Inventory Analysis

### Existing VMs and Locations
| VM Name | Config Location | Disk Location | Proposed New Name |
|---------|----------------|---------------|-------------------|
| Debian01 | D:\Hyper-V-Config-Files | D:\Hyper-V-Drives\Debian01.vhdx | debian-dev-01 |
| Elara | D:\Hyper-V-Config-Files | D:\HV\Drives\105\*.avhdx | elara-prod-01 |
| Gitlab | D:\HV\Configs\Gitlab | D:\HV\Configs\Gitlab\*.vhdx | gitlab-prod-01 |
| HV-LAN-WS2025 | D:\Hyper-V-Config-Files | D:\Hyper-V-Drives\HV-LAN-WS2025\*.avhdx | ws2025-mgmt-01 |
| LAN-HV-WS-2025-01 | D:\Hyper-V-Config-Files | D:\Hyper-V-Drives\LAN-HV-WS-2025-01.vhdx | ws2025-lan-01 |
| rightrhino | D:\HV\Configs\rightrhino | D:\HV\Configs\rightrhino\*.vhdx | rhino-dev-01 |

### VirtualBox Inventory
- **Location:** D:\VirtualBoxVMs\exact_eagle\
- **Size:** ~2.5MB (minimal VM, safe to remove)
- **Status:** Not critical, can be safely removed

## Proposed PARA Structure on Drive Z

```
Z:\Hyper-V\
├── 1_PROJECTS\
│   ├── production-vms\
│   │   ├── gitlab-prod-01\
│   │   ├── elara-prod-01\
│   │   └── ws2025-mgmt-01\
│   └── development-vms\
│       ├── debian-dev-01\
│       ├── rhino-dev-01\
│       └── ws2025-lan-01\
├── 2_AREAS\
│   ├── templates\
│   ├── iso-images\
│   └── shared-resources\
├── 3_RESOURCES\
│   ├── backups\
│   │   ├── pre-migration\
│   │   └── daily\
│   ├── documentation\
│   └── scripts\
└── 4_ARCHIVES\
    ├── original-exports\
    └── legacy-configs\
```

## Phase 1: Safe VM File Migration

### Pre-Migration Safety Checks
- [ ] Verify Z: drive has sufficient space (need ~350GB, available 870GB ✅)
- [ ] Ensure all VMs are powered off
- [ ] Create system restore point
- [ ] Document current Hyper-V settings

### Step 1.1: Create Directory Structure
```powershell
# Create PARA structure on Z: drive
New-Item -Path "Z:\Hyper-V" -ItemType Directory
New-Item -Path "Z:\Hyper-V\1_PROJECTS\production-vms" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\1_PROJECTS\development-vms" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\2_AREAS\templates" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\2_AREAS\iso-images" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\2_AREAS\shared-resources" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\3_RESOURCES\backups\pre-migration" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\3_RESOURCES\backups\daily" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\3_RESOURCES\documentation" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\3_RESOURCES\scripts" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\4_ARCHIVES\original-exports" -ItemType Directory -Force
New-Item -Path "Z:\Hyper-V\4_ARCHIVES\legacy-configs" -ItemType Directory -Force
```

### Step 1.2: Export All VMs (Safety Backup)
```powershell
# Export each VM to archives before moving
Export-VM -Name "Debian01" -Path "Z:\Hyper-V\4_ARCHIVES\original-exports"
Export-VM -Name "Elara" -Path "Z:\Hyper-V\4_ARCHIVES\original-exports"
Export-VM -Name "Gitlab" -Path "Z:\Hyper-V\4_ARCHIVES\original-exports"
Export-VM -Name "HV-LAN-WS2025" -Path "Z:\Hyper-V\4_ARCHIVES\original-exports"
Export-VM -Name "LAN-HV-WS-2025-01" -Path "Z:\Hyper-V\4_ARCHIVES\original-exports"
Export-VM -Name "rightrhino" -Path "Z:\Hyper-V\4_ARCHIVES\original-exports"
```

### Step 1.3: Copy VM Files to New Structure
```powershell
# Copy files maintaining structure - DO NOT MOVE, ONLY COPY
# Production VMs
robocopy "D:\HV\Configs\Gitlab" "Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01" /E /COPY:DAT /R:3 /W:10
robocopy "D:\HV\Drives\105" "Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\drives" /E /COPY:DAT /R:3 /W:10
robocopy "D:\Hyper-V-Drives\HV-LAN-WS2025" "Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\drives" /E /COPY:DAT /R:3 /W:10

# Development VMs
robocopy "D:\Hyper-V-Drives\Debian01.vhdx" "Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01\" /COPY:DAT /R:3 /W:10
robocopy "D:\HV\Configs\rightrhino" "Z:\Hyper-V\1_PROJECTS\development-vms\rhino-dev-01" /E /COPY:DAT /R:3 /W:10
robocopy "D:\Hyper-V-Drives\LAN-HV-WS-2025-01.vhdx" "Z:\Hyper-V\1_PROJECTS\development-vms\ws2025-lan-01\" /COPY:DAT /R:3 /W:10
```

### Step 1.4: Copy Configuration Files
```powershell
# Copy all VM configuration files
robocopy "D:\Hyper-V-Config-Files" "Z:\Hyper-V\3_RESOURCES\documentation\original-configs" /E /COPY:DAT /R:3 /W:10
```

### Safety Checkpoint 1
- [ ] Verify all files copied successfully (check file counts and sizes)
- [ ] Confirm original files on D: drive are untouched
- [ ] Document any copy errors or warnings
- [ ] Test one VM export can be imported successfully

## Phase 2: VirtualBox Cleanup

### Step 2.1: VirtualBox Assessment
- [ ] Verify exact_eagle VM is not critical (appears to be test VM)
- [ ] Check if any other applications depend on VirtualBox
- [ ] Document VirtualBox version and configuration

### Step 2.2: Safe VirtualBox Removal
```powershell
# First, backup VirtualBox VM (just in case)
robocopy "D:\VirtualBoxVMs" "Z:\Hyper-V\4_ARCHIVES\virtualbox-backup" /E /COPY:DAT /R:3 /W:10

# Uninstall VirtualBox through Programs and Features
# Or use PowerShell:
Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*VirtualBox*"} | ForEach-Object {$_.Uninstall()}
```

### Step 2.3: Clean VirtualBox Remnants
```powershell
# Remove VirtualBox directories (after backup)
Remove-Item "D:\VirtualBox" -Recurse -Force
Remove-Item "D:\VirtualBoxVMs" -Recurse -Force
```

### Safety Checkpoint 2
- [ ] Verify VirtualBox completely removed
- [ ] Confirm backup of VirtualBox files completed
- [ ] Check no system instability after removal
- [ ] Verify Hyper-V still functions normally

## Phase 3: VM Testing and Validation

### Step 3.1: Create New VMs from Copied Files
```powershell
# Import VMs to new locations (this creates new VMs, doesn't modify originals)
Import-VM -Path "Z:\Hyper-V\1_PROJECTS\production-vms\gitlab-prod-01\Virtual Machines\*.vmcx" -Copy -GenerateNewId
Import-VM -Path "Z:\Hyper-V\4_ARCHIVES\original-exports\Debian01\Virtual Machines\*.vmcx" -Copy -GenerateNewId
# Repeat for each VM
```

### Step 3.2: Update VM Storage Paths
```powershell
# For each imported VM, update disk paths to point to new locations
# Example for Debian01:
$vm = Get-VM -Name "Debian01_Copy"
$vhd = Get-VMHardDiskDrive -VM $vm
Set-VMHardDiskDrive -VM $vm -Path "Z:\Hyper-V\1_PROJECTS\development-vms\debian-dev-01\Debian01.vhdx"
```

### Step 3.3: Test Each VM
- [ ] Start each VM and verify it boots successfully
- [ ] Test network connectivity
- [ ] Verify all services start correctly
- [ ] Check disk space and performance
- [ ] Document any issues encountered

### Safety Checkpoint 3
- [ ] All VMs boot and function from new locations
- [ ] Network settings preserved correctly
- [ ] No data corruption detected
- [ ] Performance comparable to original locations
- [ ] Original VMs on D: drive still intact and functional

## Phase 4: Naming Standardization

### Step 4.1: Rename VMs in Hyper-V Manager
```powershell
# Rename VMs to follow new naming convention
Rename-VM -Name "Debian01_Copy" -NewName "debian-dev-01"
Rename-VM -Name "Elara_Copy" -NewName "elara-prod-01"
Rename-VM -Name "Gitlab_Copy" -NewName "gitlab-prod-01"
Rename-VM -Name "HV-LAN-WS2025_Copy" -NewName "ws2025-mgmt-01"
Rename-VM -Name "LAN-HV-WS-2025-01_Copy" -NewName "ws2025-lan-01"
Rename-VM -Name "rightrhino_Copy" -NewName "rhino-dev-01"
```

### Step 4.2: Update VM Configuration Paths
```powershell
# Update default VM and VHD paths in Hyper-V
Set-VMHost -VirtualMachinePath "Z:\Hyper-V\1_PROJECTS"
Set-VMHost -VirtualHardDiskPath "Z:\Hyper-V\1_PROJECTS"
```

### Step 4.3: Final Validation
- [ ] Test all renamed VMs start correctly
- [ ] Verify new naming convention is consistent
- [ ] Update any scripts or documentation referencing old names
- [ ] Confirm all VM paths point to Z: drive locations

### Safety Checkpoint 4
- [ ] All VMs operational with new names
- [ ] Hyper-V Manager shows correct paths
- [ ] No references to old D: drive locations
- [ ] System ready for original file cleanup

## Final Cleanup (Only After 100% Validation)

### Step 5.1: Remove Original VMs from Hyper-V
```powershell
# Remove original VMs from Hyper-V (keeps files on D:)
Remove-VM -Name "Debian01" -Force
Remove-VM -Name "Elara" -Force
Remove-VM -Name "Gitlab" -Force
Remove-VM -Name "HV-LAN-WS2025" -Force
Remove-VM -Name "LAN-HV-WS-2025-01" -Force
Remove-VM -Name "rightrhino" -Force
```

### Step 5.2: Archive Original Files
```powershell
# Move (not delete) original files to archive location
robocopy "D:\HV" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\HV" /MOVE /E
robocopy "D:\Hyper-V-Config-Files" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\Hyper-V-Config-Files" /MOVE /E
robocopy "D:\Hyper-V-Drives" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\Hyper-V-Drives" /MOVE /E
robocopy "D:\Hyper-V-Exports" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\Hyper-V-Exports" /MOVE /E
```

### Final Safety Checkpoint
- [ ] All VMs running from Z: drive locations
- [ ] D: drive cleared of VM files
- [ ] Original files safely archived on Z: drive
- [ ] System stable and performing well
- [ ] Ready for Proxmox installation on D: drive

---

**Implementation Status:** Ready to begin  
**Estimated Time:** 2-3 days with thorough testing  
**Rollback Plan:** Restore from exports in 4_ARCHIVES if needed
