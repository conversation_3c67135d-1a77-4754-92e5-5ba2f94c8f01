# Hyper-V Migration Safety Checklist

**Project:** Safe VM File Reorganization  
**Critical:** Complete ALL items before proceeding to next phase  
**Goes in:** 1.Projects - active infrastructure optimization project

## Pre-Migration Safety Verification

### System State Check
- [ ] All VMs are currently powered off
- [ ] No critical services running on VMs
- [ ] System restore point created
- [ ] Current date/time documented: _______________

### Storage Verification
- [ ] Z: drive has 870GB+ free space (need ~350GB)
- [ ] D: drive current usage documented
- [ ] All drives healthy (no errors in Event Viewer)
- [ ] No pending disk operations or defragmentation

### Backup Verification
- [ ] System backup completed within last 24 hours
- [ ] Backup tested and verified restorable
- [ ] External backup media available if needed
- [ ] Network backup location accessible

### Documentation Prepared
- [ ] Current VM inventory documented
- [ ] Network configurations recorded
- [ ] Service dependencies mapped
- [ ] Emergency contact list prepared

## Phase-by-Phase Safety Gates

### Phase 1: File Migration Safety Gate
**STOP - Do not proceed until ALL items checked:**

- [ ] Directory structure created successfully on Z: drive
- [ ] All VM exports completed without errors
- [ ] File copy operations completed with exit codes ≤ 7
- [ ] File count verification: source vs destination matches
- [ ] File size verification: no truncated files detected
- [ ] Original files on D: drive completely untouched
- [ ] No disk space warnings on any drive
- [ ] System performance normal during copy operations

**Validation Commands:**
```powershell
# Verify exports exist
Get-ChildItem "Z:\Hyper-V\4_ARCHIVES\original-exports" -Directory

# Check file counts
$sourceFiles = (Get-ChildItem "D:\HV" -Recurse -File).Count
$destFiles = (Get-ChildItem "Z:\Hyper-V\1_PROJECTS" -Recurse -File).Count
Write-Host "Source files: $sourceFiles, Destination files: $destFiles"

# Verify disk space
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="FreeGB";Expression={[math]::Round($_.FreeSpace/1GB,2)}}
```

### Phase 2: VirtualBox Cleanup Safety Gate
**STOP - Do not proceed until ALL items checked:**

- [ ] VirtualBox files backed up to Z: drive
- [ ] VirtualBox uninstalled successfully
- [ ] No VirtualBox processes running
- [ ] VirtualBox directories removed from D: drive
- [ ] System reboot completed successfully
- [ ] Hyper-V still functional after VirtualBox removal
- [ ] No system errors in Event Viewer

**Validation Commands:**
```powershell
# Check for VirtualBox processes
Get-Process | Where-Object {$_.Name -like "*VirtualBox*"}

# Verify backup
Test-Path "Z:\Hyper-V\4_ARCHIVES\virtualbox-backup"

# Check Hyper-V status
Get-WindowsFeature -Name "Hyper-V"
```

### Phase 3: VM Testing Safety Gate
**STOP - Do not proceed until ALL items checked:**

- [ ] Test VM import successful from new location
- [ ] VM boots without errors
- [ ] Network connectivity verified
- [ ] All services start correctly
- [ ] No data corruption detected
- [ ] Performance comparable to original
- [ ] VM can be stopped and started reliably
- [ ] Original VM still functional on D: drive (fallback ready)

**Validation Commands:**
```powershell
# Test import (example for one VM)
Import-VM -Path "Z:\Hyper-V\4_ARCHIVES\original-exports\Debian01" -Copy -GenerateNewId

# Check VM status
Get-VM | Select-Object Name, State, Status
```

### Phase 4: Naming Standardization Safety Gate
**STOP - Do not proceed until ALL items checked:**

- [ ] All VMs renamed successfully
- [ ] New names follow consistent convention
- [ ] VM configurations updated with new paths
- [ ] Hyper-V Manager shows correct locations
- [ ] All VMs boot with new names
- [ ] No broken references or dependencies
- [ ] Documentation updated with new names
- [ ] Team notified of naming changes

## Emergency Procedures

### If Phase 1 Fails
1. **STOP all operations immediately**
2. Check disk space and system health
3. Verify original files on D: drive are intact
4. Delete incomplete copies on Z: drive
5. Review error logs and resolve issues
6. Restart Phase 1 from beginning

### If Phase 2 Fails
1. **STOP VirtualBox removal**
2. Restore VirtualBox from backup if needed
3. Verify Hyper-V functionality
4. Check system stability
5. Resolve conflicts before proceeding

### If Phase 3 Fails
1. **STOP VM migration immediately**
2. Remove failed VM imports
3. Verify original VMs still work on D: drive
4. Restore from exports if needed
5. Investigate and resolve VM issues

### If Phase 4 Fails
1. **STOP renaming operations**
2. Revert VM names to previous state
3. Restore original configurations
4. Verify all VMs functional
5. Plan corrective actions

## Rollback Procedures

### Complete Rollback (Nuclear Option)
1. **Stop all VMs**
2. Remove all new VMs from Hyper-V Manager
3. Restore original VM registrations
4. Point Hyper-V back to D: drive paths
5. Import VMs from original exports if needed
6. Verify all original functionality restored
7. Document lessons learned

### Partial Rollback (Single VM)
1. Stop problematic VM
2. Remove from Hyper-V Manager
3. Import from backup export
4. Test functionality
5. Continue with other VMs

## Success Validation

### Technical Validation
- [ ] All VMs operational from Z: drive locations
- [ ] No performance degradation detected
- [ ] All network connectivity maintained
- [ ] No data loss or corruption
- [ ] Consistent naming convention applied
- [ ] PARA structure properly implemented

### Operational Validation
- [ ] Hyper-V Manager shows clean organization
- [ ] Backup procedures updated for new locations
- [ ] Documentation reflects new structure
- [ ] Team trained on new naming conventions
- [ ] Monitoring updated for new paths

## Final Cleanup Authorization

**DANGER ZONE - Only proceed after 100% validation**

### Before D: Drive Cleanup
- [ ] All VMs tested for minimum 24 hours from Z: drive
- [ ] No issues reported by users or monitoring
- [ ] Full system backup completed
- [ ] Management approval obtained
- [ ] Maintenance window scheduled

### D: Drive Cleanup Steps
1. Archive (don't delete) original files to Z: drive archives
2. Remove original VM registrations from Hyper-V
3. Move (don't delete) files from D: drive to archives
4. Verify D: drive is clean and ready for Proxmox
5. Update all documentation and procedures

## Emergency Contacts

- **Primary Contact:** Infrastructure Team Lead
- **Secondary Contact:** System Administrator  
- **Escalation:** IT Management
- **Vendor Support:** Microsoft Hyper-V Support (if needed)

## Sign-off Requirements

### Phase Completion Sign-offs
- [ ] Phase 1 completed by: _________________ Date: _______
- [ ] Phase 2 completed by: _________________ Date: _______
- [ ] Phase 3 completed by: _________________ Date: _______
- [ ] Phase 4 completed by: _________________ Date: _______

### Final Project Sign-off
- [ ] Technical validation completed by: _________________ Date: _______
- [ ] Operational validation completed by: _________________ Date: _______
- [ ] Management approval for cleanup: _________________ Date: _______
- [ ] Project completion certified by: _________________ Date: _______

---

**Remember:** When in doubt, STOP and seek assistance. Data safety is paramount.  
**Motto:** "Measure twice, cut once" - Validate everything before proceeding.
