# AI Development Guidelines

## Communication Patterns

### Clear Request Structure
- **State your goal first**: "I need to create a user authentication system"
- **Provide context**: "This is for a web app with 1000+ users"
- **Specify constraints**: "Must work with existing MySQL database"
- **Ask for confirmation**: "Does this approach make sense?"

### Effective Information Sharing
- Share relevant code snippets when asking for modifications
- Mention your skill level: "I'm new to Python" or "I have basic JavaScript knowledge"
- Describe what you've already tried: "I attempted X but got error Y"

**Example**: Instead of "Fix my login", say: "My login form isn't validating passwords correctly. I'm using Node.js with bcrypt. The error message is 'Cannot read property hash of undefined'. Here's my current code..."

## Project Planning with AI

### Break Down Large Tasks
1. **Start with the big picture**: "Build an inventory management system"
2. **Identify major components**: Database, API, frontend, authentication
3. **Create step-by-step plan**: Let AI help sequence the work
4. **Define success criteria**: What does "done" look like?

### Use Iterative Development
- Build one feature completely before starting the next
- Test each component as you build it
- Get AI feedback on your approach before writing lots of code

**Example Planning Session**:
- "I want to build a customer database for my small business"
- AI suggests: Database design → Basic CRUD operations → Simple web interface → Search functionality
- You confirm: "Yes, let's start with database design"

## Task Management and Tracking

### Document Your Progress
- Keep a simple log of what you've completed
- Note any problems you encountered and solutions
- Track which AI suggestions worked vs. didn't work

### Version Control Basics
- Save working versions before making big changes
- Use descriptive names: `customer-db-v1-working.py` not `test.py`
- Keep backups of important files

## Code Organization with PARA

### File Placement Logic
- **1.Projects/**: Active development work with deadlines
  - Example: `1.Projects/ecommerce-site/` (launching next month)
- **2.Areas/**: Ongoing maintenance and operations
  - Example: `2.Areas/server-monitoring/` (continuous responsibility)
- **3.Resources/**: Reference materials and reusable components
  - Example: `3.Resources/code-templates/` (for future use)
- **4.Archives/**: Completed or abandoned projects
  - Example: `4.Archives/old-website-2023/` (no longer active)

### Code Structure Within Projects
```
1.Projects/my-app/
├── src/           # Source code
├── tests/         # Test files
├── docs/          # Documentation
├── config/        # Configuration files
└── README.md      # Project overview
```

## Basic Testing Approaches

### Start Simple
- Test the most important functions first
- Use real data that matches your use case
- Write down what you expect to happen before running tests

### Common Testing Pattern
1. **Arrange**: Set up your test data
2. **Act**: Run the function you're testing
3. **Assert**: Check if the result matches expectations

**Example**:
```javascript
// Test a simple calculator function
const result = add(2, 3);
if (result === 5) {
    console.log("✓ Addition test passed");
} else {
    console.log("✗ Addition test failed");
}
```

### When to Ask for Help
- If tests keep failing and you don't understand why
- When you need to test complex scenarios
- If you're unsure what to test

## Working Across Multiple Environments

### Environment-Specific Considerations
- **macOS**: Use Terminal, be aware of case-sensitive file system
- **Windows Server 2022**: Use PowerShell, watch for path separators
- **Linux (Debian/Ubuntu)**: Use bash, check permissions carefully

### VM and Baremetal Management
- Document which environment each project runs on
- Keep environment setup instructions in project README
- Test critical changes on staging environment first

### Cross-Platform Code Tips
- Use relative paths when possible
- Avoid hardcoded system-specific paths
- Test on your primary deployment environment

## Documentation Standards

### Essential Documentation
- **README.md**: What the project does, how to run it
- **SETUP.md**: Installation and configuration steps
- **CHANGELOG.md**: What changed in each version

### Code Comments
- Explain WHY you did something, not just what
- Document any workarounds or temporary solutions
- Note any external dependencies or requirements

**Good Comment Example**:
```python
# Using sleep() here because the API rate limits to 1 request/second
# TODO: Replace with proper rate limiting library
time.sleep(1)
```

## Getting Maximum Value from AI

### Before Asking for Code
- Explain your business goal, not just the technical requirement
- Share your timeline and constraints
- Mention your experience level with the technology

### After Receiving Code
- Read through the code before running it
- Ask questions about parts you don't understand
- Test the code with your actual data
- Request explanations for complex logic

### Building Your Skills
- Ask AI to explain concepts you encounter repeatedly
- Request simpler alternatives when code seems too complex
- Practice modifying AI-generated code to fit your specific needs
