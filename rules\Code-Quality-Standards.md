### Code Review Requirements
**Every code change must include:**
1. **Clear purpose**: What problem does this solve?
2. **Testing evidence**: How was this verified to work?
3. **Error handling**: What happens when things go wrong?
4. **Documentation**: Comments for complex logic

### Minimum Quality Checklist
- [ ] Code follows consistent formatting
- [ ] Functions have single, clear purposes
- [ ] Error conditions are handled appropriately
- [ ] No hardcoded values that should be configurable
- [ ] Code includes basic input validation
- [ ] Relative paths are used when possible

### Performance Considerations
- Avoid nested loops with large datasets
- Close database connections and file handles
- Use appropriate data structures for the task
- Consider memory usage for long-running processes