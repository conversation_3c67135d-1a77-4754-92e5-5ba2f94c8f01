---
type: "manual"
---


### Mandatory Confirmation for Breaking Changes
**ALWAYS confirm before executing commands that:**
- Delete files or directories
- Modify system configurations
- Install or uninstall software packages
- Change file permissions
- Restart services or systems
- Modify database schemas
- Deploy code to production environments

### Breaking Change Identification
**These operations require explicit user approval:**
```bash
# File operations
rm, rmdir, del, Remove-Item
mv, move (when overwriting existing files)
chmod, chown (permission changes)

# System operations  
sudo, su (privilege escalation)
systemctl restart, service restart
reboot, shutdown

# Package management
apt install/remove, yum install/remove
npm install -g, pip install --global
```

### Safe Command Execution
**These commands can be executed without confirmation:**
- File viewing: `cat`, `less`, `head`, `tail`, `ls`, `dir`
- Directory navigation: `cd`, `pwd`
- File creation: `touch`, `echo >` (new files only)
- Status checking: `ps`, `top`, `systemctl status`
- Network testing: `ping`, `curl` (read-only operations)

### Confirmation Format
When breaking changes are needed, present:
1. **What will be changed**: Specific files/systems affected
2. **Why it's necessary**: Business justification
3. **Potential risks**: What could go wrong
4. **Rollback plan**: How to undo if needed

**Example**:
```
⚠️  BREAKING CHANGE CONFIRMATION REQUIRED ⚠️

Action: Delete old log files in /var/log/myapp/
Files affected: 15 files older than 30 days (approximately 2GB)
Reason: Free up disk space (currently at 85% capacity)
Risk: Loss of historical log data for debugging
Rollback: Cannot be undone - logs will be permanently deleted

Proceed? (yes/no):
```