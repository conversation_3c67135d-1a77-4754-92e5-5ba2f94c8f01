# Network Architecture Planning Document

**Goes in 1.Projects** - Active network planning document for infrastructure deployment project.

## Document Information
- **Version:** 1.0
- **Created:** 2025-07-18
- **Author:** Infrastructure Team
- **Status:** Planning Phase
- **Review Date:** 2025-07-25

## Executive Summary
This document outlines the comprehensive network architecture plan for the infrastructure management environment. The design focuses on security, scalability, and ease of management for a beginner-friendly multi-platform environment.

## Network Requirements

### Business Requirements
- **Security:** Protect against external and internal threats
- **Performance:** Ensure adequate bandwidth for all services
- **Reliability:** Minimize downtime and single points of failure
- **Scalability:** Support future growth and expansion
- **Manageability:** Simple administration for beginners

### Technical Requirements
- **Subnet:** *************/24 primary LAN
- **Router:** FreshTomato firmware for advanced features
- **DNS:** Private DNS server with .moc domain
- **VPN:** WireGuard for secure remote access
- **Monitoring:** Network performance and security monitoring

## Network Design

### IP Address Allocation Plan

#### Primary Subnet: *************/24
```
Network: *************/24
Subnet Mask: *************
Gateway: ************* (FreshTomato Router)
Broadcast: ***************
Usable IPs: ************* - ***************
```

#### VLAN Segmentation Strategy
| VLAN ID | Subnet | Purpose | IP Range |
|---------|--------|---------|----------|
| 10 | *************0/28 | Management | .10-.15 |
| 20 | **************/26 | Servers | .20-.83 |
| 30 | **************/26 | Virtualization | .84-.147 |
| 40 | ***************/28 | DMZ | .148-.163 |
| 50 | ***************/26 | Guest/IoT | .164-.227 |
| 99 | ***************/28 | Reserved | .228-.243 |

### Network Topology

```
Internet
    |
ISP Router (WAN Only)
    |
FreshTomato Router (*************)
    |
+-- Management VLAN 10 (*************0/28)
|   +-- Network Management Server (.11)
|   +-- Monitoring Server (.12)
|   +-- DNS Server (.13)
|   +-- Admin Workstation (.14)
|
+-- Server VLAN 20 (**************/26)
|   +-- Windows Server 2025 (.21)
|   +-- Linux File Server (.22)
|   +-- Database Server (.23)
|   +-- Web Server (.24)
|
+-- Virtualization VLAN 30 (**************/26)
|   +-- Hyper-V Host (.85)
|   +-- Proxmox VE (.86)
|   +-- VM Pool (.87-.120)
|   +-- Container Pool (.121-.140)
|
+-- DMZ VLAN 40 (***************/28)
|   +-- External Web Server (.149)
|   +-- VPN Gateway (.150)
|   +-- Mail Server (.151)
|
+-- Guest VLAN 50 (***************/26)
    +-- Guest Devices (.165-.200)
    +-- IoT Devices (.201-.220)
```

## DNS Strategy

### Private DNS Server Configuration
- **Primary DNS:** *************3 (Internal DNS Server)
- **Secondary DNS:** ******* (Cloudflare)
- **Domain:** .moc (internal domain)
- **Reverse DNS:** Configured for all internal subnets

### DNS Records Plan
```
# A Records (Forward DNS)
router.moc          *************
dns.moc             *************3
mgmt.moc            *************1
monitor.moc         *************2
win01.moc           **************
linux01.moc        **************
db01.moc            **************
web01.moc           **************
hyperv.moc          **************
proxmox.moc         **************

# CNAME Records
gateway.moc         router.moc
ns1.moc             dns.moc
```

### DNS Security Features
- **DNS Filtering:** Block malicious domains
- **DNS over HTTPS:** Encrypt external DNS queries
- **Local Resolution:** Reduce external dependencies
- **Monitoring:** Log and analyze DNS queries

## Security Framework

### Network Security Layers

#### 1. Perimeter Security
- **ISP Router:** Basic firewall, WAN connectivity only
- **FreshTomato Router:** Advanced firewall, intrusion detection
- **DMZ:** Isolated zone for external-facing services

#### 2. Internal Segmentation
- **VLAN Isolation:** Separate network segments
- **Inter-VLAN Rules:** Controlled communication between segments
- **Micro-segmentation:** Granular access controls

#### 3. Access Control
- **Management Access:** Restricted to management VLAN
- **Service Access:** Controlled by firewall rules
- **Guest Isolation:** Complete isolation from internal resources

### Firewall Rules Framework

#### Default Policies
```
# Default deny all traffic
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow established connections
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
iptables -A FORWARD -m state --state ESTABLISHED,RELATED -j ACCEPT
```

#### Management VLAN Rules
```
# Allow management access to all VLANs
ALLOW *************0/28 -> *************/24 port 22,443,3389,161

# Allow DNS queries
ALLOW *************/24 -> *************3 port 53

# Allow NTP synchronization
ALLOW *************/24 -> *************1 port 123
```

#### Server VLAN Rules
```
# Allow web traffic
ALLOW *************/24 -> **************/26 port 80,443

# Allow database access from web servers
ALLOW ************** -> ************** port 3306,5432

# Allow file sharing
ALLOW **************/26 -> ************** port 445,2049
```

#### Guest VLAN Rules
```
# Allow internet access only
ALLOW ***************/26 -> 0.0.0.0/0 port 80,443

# Deny access to internal networks
DENY ***************/26 -> *************/19

# Allow DNS queries
ALLOW ***************/26 -> *************3 port 53
```

## VPN Implementation

### WireGuard Configuration

#### Server Configuration
```
[Interface]
PrivateKey = [SERVER_PRIVATE_KEY]
Address = ********/24
ListenPort = 51820
PostUp = iptables -A FORWARD -i wg0 -j ACCEPT
PostDown = iptables -D FORWARD -i wg0 -j ACCEPT

[Peer]
PublicKey = [CLIENT_PUBLIC_KEY]
AllowedIPs = ********/32
```

#### Client Access Profiles
- **Administrator:** Full network access
- **Remote Worker:** Limited server access
- **Vendor:** Specific system access only
- **Monitoring:** Read-only access to monitoring systems

### Site-to-Site VPN (Hetzner)
```
# Hetzner to Local Network
Local Network: *************/24
Remote Network: ********/24
VPN Gateway: [Hetzner Public IP]
Protocol: WireGuard
Encryption: ChaCha20-Poly1305
```

## Quality of Service (QoS)

### Traffic Prioritization
| Priority | Traffic Type | Bandwidth Allocation |
|----------|--------------|---------------------|
| High | Management, VoIP | 20% guaranteed |
| Medium | Business Applications | 50% guaranteed |
| Low | Guest, Bulk Transfer | 30% remaining |

### Bandwidth Management
- **Total Bandwidth:** [ISP Speed]
- **Upload Limit:** 80% of ISP upload
- **Download Limit:** 90% of ISP download
- **Burst Allowance:** 120% for short periods

## Monitoring and Alerting

### Network Monitoring Points
- **Router Performance:** CPU, memory, throughput
- **Link Utilization:** Bandwidth usage per VLAN
- **Security Events:** Firewall blocks, intrusion attempts
- **DNS Performance:** Query response times, failures

### Alert Thresholds
| Metric | Warning | Critical |
|--------|---------|----------|
| Bandwidth Usage | 70% | 85% |
| Packet Loss | 1% | 5% |
| Latency | 50ms | 100ms |
| DNS Failures | 5% | 10% |

## Implementation Timeline

### Phase 1: Foundation (Week 1-2)
- Configure FreshTomato router
- Set up basic VLAN structure
- Implement initial firewall rules
- Test basic connectivity

### Phase 2: Services (Week 3-4)
- Deploy DNS server
- Configure DHCP scopes
- Implement QoS policies
- Set up monitoring

### Phase 3: Security (Week 5-6)
- Deploy advanced firewall rules
- Configure VPN services
- Implement intrusion detection
- Security testing and validation

### Phase 4: Optimization (Week 7-8)
- Performance tuning
- Documentation completion
- User training
- Final testing and handover

## Risk Assessment

### High-Risk Items
| Risk | Impact | Mitigation |
|------|--------|------------|
| Router failure | High | Backup router, configuration backups |
| DNS outage | High | Secondary DNS, external fallback |
| Security breach | High | Layered security, monitoring |
| Configuration error | Medium | Change management, testing |

### Contingency Plans
- **Router Failure:** Backup router with saved configuration
- **Internet Outage:** Cellular backup connection
- **Security Incident:** Isolation procedures, incident response
- **Performance Issues:** Traffic shaping, bandwidth upgrades

## Testing and Validation

### Connectivity Testing
- Ping tests between all VLANs
- DNS resolution testing
- Internet connectivity verification
- VPN connection testing

### Security Testing
- Port scanning from external sources
- VLAN isolation verification
- Firewall rule validation
- Intrusion detection testing

### Performance Testing
- Bandwidth throughput testing
- Latency measurements
- QoS policy verification
- Load testing under stress

## Documentation Deliverables

### Technical Documentation
- Network topology diagrams
- IP address management (IPAM) spreadsheet
- Firewall rule documentation
- VPN configuration guides
- Troubleshooting procedures

### Operational Documentation
- Daily monitoring checklists
- Maintenance procedures
- Emergency response procedures
- Contact information and escalation paths

---

*This planning document serves as the foundation for network architecture implementation. All configurations should be tested in a lab environment before production deployment.*

**Document Status:** Planning Phase  
**Next Review:** 2025-07-25  
**Implementation Start:** Upon approval
