---
type: "manual"
---

### Sensitive Information Protection
**NEVER include in code or logs:**
- API keys, passwords, tokens
- Database connection strings with credentials
- Personal identifiable information (PII)
- Financial data or payment information
- Internal system architecture details

### Environment Variable Usage
**Always use environment variables for:**
- Database credentials
- API keys and secrets
- Third-party service tokens
- Configuration that differs between environments

**Example**:
```python
# ✓ Correct
db_password = os.getenv('DB_PASSWORD')

# ✗ Wrong
db_password = 'mySecretPassword123'
```

### Code Repository Safety
- Never commit sensitive data to version control
- Use `.gitignore` for environment files and secrets
- Scan code for accidentally committed credentials before pushing