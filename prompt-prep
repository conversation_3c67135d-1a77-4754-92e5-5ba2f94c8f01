Create two separate documents for an AI coding agent:

1. **General Guidelines Document** - Best practices for AI-assisted development
2. **Rules Document** - Specific constraints and requirements

**Target Audience**: Complete beginner with entrepreneurial goals who wants to use AI tools to build a business

**Requirements**:
- Use clear, simple language (avoid technical jargon)
- Keep each document concise (maximum 2 pages each)
- Focus on actionable items, not theory
- Include specific examples where helpful
- Structure with bullet points and short paragraphs for easy scanning
- include rules for working in the users command line, 
- confirming before any potential breaking changes


the users envrinoment is vscode
the ai agent is augment code
the user works in MacOS, Windows Server 20225,and Linux (Debian and Ubuntu) with multiple vm's
the user uses PARA methodolgy for folder structures, and wants the AI to use PARA as a reference for folder structures and when adding files to the PARA folder structure explain logic behind it 

The user uses PARA methodolgy for folder structures, please use that as a reference for folder structures and when adding files to the PARA folder structure explain logic behind it 

Folder structure
0.Inbox
1.Projects
2.Areas
3.Resources
4.Archives


**Content Focus**:
- Guidelines: How to effectively communicate with AI, project planning, code organization, testing basics
- Rules: What the AI should/shouldn't do, security considerations, code quality standards, documentation requirements

**Format**: Markdown format with clear headings and sections

**Tone**: consice, it is for an AI agent , no fluff, just clear instructions and rules



Create two separate markdown documents for configuring an AI coding agent (Augment Code):

## Document 1: `ai-guidelines.md`
**Purpose**: Best practices for AI-assisted development workflow
**Location**: Place in `3.Resources/` folder (reference material for ongoing use)

## Document 2: `ai-rules.md` 
**Purpose**: Strict constraints and operational requirements
**Location**: Place in `3.Resources/` folder (reference material for ongoing use)

**Target User**: Complete beginner entrepreneur using AI tools to build a business and maintain IT infrastructure

**Document Requirements**:
- Maximum 2 pages each (approximately 500-800 words)
- Use bullet points and numbered lists for scannability
- Include 2-3 concrete examples per major section
- Avoid technical jargon - define any necessary terms
- Focus on actionable directives, not explanatory theory

**Environment Context**:
- Development environment: VSCode
- AI agent: Augment Code
- Operating systems: macOS, Windows Server 2022, Linux (Debian/Ubuntu)
- Infrastructure: Multiple VMs and Baremetal machines
- File organization: PARA methodology (0.Inbox, 1.Projects, 2.Areas, 3.Resources, 4.Archives)

**Required Content Areas**:

### Guidelines Document Must Include:
- Effective AI communication patterns
- Project planning with AI assistance
- Task management and tracking
- Code and documentorganization strategies (including PARA folder structure)
- Basic testing approaches
- PARA folder structure usage with rationale

### Rules Document Must Include:
- Command line interaction protocols
- Mandatory confirmation requirements for breaking changes
- Security constraints and data handling
- Code quality standards and review processes
- Documentation requirements
- File creation and organization rules using PARA methodology

**Special Requirements**:
- When AI suggests PARA folder placement, must explain the logic (why this folder vs others)
- All breaking command line operations require explicit user confirmation
- Breaking changes must be clearly identified and approved before execution

**Format**: Standard markdown with clear H1/H2/H3 headings, consistent formatting

**Tone**: Direct, concise, instruction-focused - written for AI consumption, not human reading pleasure