# Phase 2: Physical-to-Virtual (P2V) Migration Implementation Guide

**Project:** Complete Virtualization Migration  
**Phase:** 2 of 2 - P2V Migration of Windows Server 2025 Host  
**Target:** Convert physical Windows Server 2025 to Proxmox VM  
**Goes in:** 1.Projects - active infrastructure optimization project

## Pre-Phase 2 Requirements

### Prerequisites Checklist
- [ ] **Phase 1 Complete:** All 6 VMs successfully migrated to Proxmox
- [ ] **VMs Validated:** All migrated VMs tested and functional
- [ ] **Proxmox Stable:** Proxmox VE running reliably with all VMs
- [ ] **Network Configured:** All VM network settings updated
- [ ] **Backups Created:** Current Proxmox backup schedule operational

### Current Environment Status
- **Physical Host:** Windows Server 2025 Datacenter (AXIOM)
- **Hardware:** Intel i9-10900, 64GB RAM, 4x SSDs
- **Migrated VMs:** 6 VMs now running natively on Proxmox
- **Remaining Task:** Virtualize the physical Windows Server host

## Phase 2 Implementation Strategy

### 2.1 Migration Approach
**Goal:** Convert the physical Windows Server 2025 host into a Proxmox VM, creating a fully virtualized environment where:
- Proxmox VE runs on bare metal
- All original VMs run as native Proxmox guests
- Windows Server 2025 runs as a Proxmox VM (no nested virtualization)

### 2.2 Final Architecture
```
Physical Hardware (Intel i9-10900, 64GB RAM)
├── Proxmox VE (Bare Metal Hypervisor)
    ├── debian-dev-01 (VM ID: 101)
    ├── rhino-dev-01 (VM ID: 102)
    ├── ws2025-lan-01 (VM ID: 103)
    ├── gitlab-prod-01 (VM ID: 201)
    ├── elara-prod-01 (VM ID: 202)
    ├── ws2025-mgmt-01 (VM ID: 203)
    └── windows-server-2025 (VM ID: 301) ← P2V Result
```

## Step 1: Pre-P2V Preparation

### 1.1 Clean Up Windows Server Host
```powershell
# Remove all Hyper-V VMs (already migrated)
Get-VM | Remove-VM -Force

# Remove Hyper-V role (optional, but recommended)
Disable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# Clean up old VM files on D: drive
# Move to archive location first for safety
robocopy "D:\HV" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\HV" /MOVE /E
robocopy "D:\Hyper-V-Config-Files" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\Hyper-V-Config-Files" /MOVE /E
robocopy "D:\Hyper-V-Drives" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\Hyper-V-Drives" /MOVE /E
robocopy "D:\Hyper-V-Exports" "Z:\Hyper-V\4_ARCHIVES\original-d-drive\Hyper-V-Exports" /MOVE /E

# Verify D: drive is clean
Get-ChildItem D:\ -Directory
```

### 1.2 System Optimization for P2V
```powershell
# Disable unnecessary services
Set-Service -Name "vmms" -StartupType Disabled -ErrorAction SilentlyContinue
Set-Service -Name "vmickvpexchange" -StartupType Disabled -ErrorAction SilentlyContinue
Set-Service -Name "vmicguestinterface" -StartupType Disabled -ErrorAction SilentlyContinue

# Clean temporary files
cleanmgr /sagerun:1

# Defragment system drive (if not SSD)
# Skip if C: is SSD - check with: Get-PhysicalDisk

# Update Windows
Install-Module PSWindowsUpdate -Force
Get-WUInstall -AcceptAll -AutoReboot
```

### 1.3 Document Current Configuration
```powershell
# Export current system configuration
Get-ComputerInfo | Out-File "Z:\Hyper-V\3_RESOURCES\documentation\pre-p2v-system-info.txt"
Get-Service | Where-Object {$_.Status -eq "Running"} | Out-File "Z:\Hyper-V\3_RESOURCES\documentation\pre-p2v-services.txt"
Get-NetAdapter | Out-File "Z:\Hyper-V\3_RESOURCES\documentation\pre-p2v-network.txt"
ipconfig /all | Out-File "Z:\Hyper-V\3_RESOURCES\documentation\pre-p2v-network-config.txt"
```

## Step 2: Create P2V Image with Disk2VHD

### 2.1 Download and Prepare Disk2VHD
```powershell
# Download Disk2VHD from Microsoft Sysinternals
# https://docs.microsoft.com/en-us/sysinternals/downloads/disk2vhd

# Extract to C:\Tools\Disk2VHD\
New-Item -Path "C:\Tools\Disk2VHD" -ItemType Directory -Force
# Extract disk2vhd.exe to this location
```

### 2.2 Create P2V Image
```powershell
# Navigate to Disk2VHD location
cd "C:\Tools\Disk2VHD"

# Create VHDX of system drive
# Target location: Z: drive for space
.\disk2vhd.exe -accepteula C: "Z:\Hyper-V\3_RESOURCES\p2v\windows-server-2025-p2v.vhdx"

# Monitor progress - this will take significant time (several hours)
# File size will be approximately 100-200GB depending on C: drive usage
```

### 2.3 Verify P2V Image Creation
```powershell
# Check created VHDX file
Get-ChildItem "Z:\Hyper-V\3_RESOURCES\p2v\" -File | Select-Object Name, Length, CreationTime

# Verify VHDX integrity
Get-VHD -Path "Z:\Hyper-V\3_RESOURCES\p2v\windows-server-2025-p2v.vhdx" | Select-Object Path, VhdType, Size, FileSize
```

## Step 3: Transfer P2V Image to Proxmox

### 3.1 Prepare Proxmox for P2V Import
```bash
# On Proxmox host, create P2V directory
mkdir -p /tmp/p2v-migration

# Create storage space for large P2V image
# Ensure sufficient space (200GB+) on target storage
df -h /var/lib/vz/
```

### 3.2 Transfer VHDX to Proxmox
```bash
# Option A: Network transfer (if network share available)
mount -t cifs //AXIOM/Z$ /mnt/windows-share -o username=administrator
cp /mnt/windows-share/Hyper-V/3_RESOURCES/p2v/windows-server-2025-p2v.vhdx /tmp/p2v-migration/

# Option B: External storage transfer
# Copy VHDX to external USB drive, then to Proxmox

# Option C: Direct SCP (if SSH enabled on Windows)
scp administrator@AXIOM:/Z/Hyper-V/3_RESOURCES/p2v/windows-server-2025-p2v.vhdx /tmp/p2v-migration/
```

## Step 4: Convert P2V Image to Proxmox Format

### 4.1 Convert VHDX to qcow2
```bash
# Navigate to P2V directory
cd /tmp/p2v-migration

# Convert VHDX to qcow2 (this will take several hours)
qemu-img convert -p -O qcow2 windows-server-2025-p2v.vhdx windows-server-2025.qcow2

# Verify conversion
qemu-img info windows-server-2025.qcow2
```

### 4.2 Optimize Converted Image
```bash
# Compress qcow2 image to save space
qemu-img convert -p -O qcow2 -c windows-server-2025.qcow2 windows-server-2025-compressed.qcow2

# Compare sizes
ls -lh windows-server-2025*.qcow2
```

## Step 5: Create Windows Server VM in Proxmox

### 5.1 Create VM with Appropriate Resources
```bash
# Create Windows Server 2025 VM (VM ID: 301)
qm create 301 \
  --name "windows-server-2025" \
  --memory 16384 \
  --cores 8 \
  --sockets 1 \
  --cpu x86-64-v2-AES \
  --net0 virtio,bridge=vmbr0 \
  --scsihw virtio-scsi-single \
  --ostype win11 \
  --bios ovmf \
  --efidisk0 local-lvm:1,format=qcow2 \
  --agent enabled=1

# Import P2V disk
qm disk import 301 /tmp/p2v-migration/windows-server-2025-compressed.qcow2 local-lvm

# Attach disk as IDE initially (for boot compatibility)
qm set 301 --ide0 local-lvm:vm-301-disk-1

# Set boot order
qm set 301 --boot order=ide0

# Add VirtIO drivers ISO
qm set 301 --ide2 local:iso/virtio-win.iso,media=cdrom
```

### 5.2 Initial Boot Configuration
```bash
# Start VM for first boot
qm start 301

# Monitor boot process
qm monitor 301

# Access console if needed
qm terminal 301
```

## Step 6: Post-P2V Configuration

### 6.1 Windows Boot Repair (if needed)
If the P2V VM doesn't boot properly:

1. **Boot from Windows Recovery:**
   - Attach Windows Server 2025 ISO
   - Boot from ISO and select "Repair your computer"
   - Use "Startup Repair" or "Command Prompt"

2. **Manual Boot Repair:**
```cmd
# In Windows Recovery Command Prompt
bootrec /fixmbr
bootrec /fixboot
bootrec /rebuildbcd
```

### 6.2 Install VirtIO Drivers
Once Windows boots successfully:

1. **Install VirtIO Network Driver:**
   - Device Manager → Network adapters
   - Update driver from VirtIO ISO

2. **Install VirtIO Storage Driver:**
   - Device Manager → Storage controllers
   - Update driver from VirtIO ISO

3. **Switch to VirtIO SCSI:**
```bash
# After drivers installed, shutdown VM
qm shutdown 301

# Switch to VirtIO SCSI
qm set 301 --scsi0 local-lvm:vm-301-disk-1
qm set 301 --delete ide0
qm set 301 --boot order=scsi0

# Restart VM
qm start 301
```

### 6.3 Network Configuration
```powershell
# Inside Windows Server VM
# Reconfigure network settings
Get-NetAdapter | Rename-NetAdapter -NewName "Ethernet"
New-NetIPAddress -InterfaceAlias "Ethernet" -IPAddress ************* -PrefixLength 24 -DefaultGateway ***********
Set-DnsClientServerAddress -InterfaceAlias "Ethernet" -ServerAddresses *******,*******
```

### 6.4 Install Proxmox Guest Agent
```powershell
# Download and install QEMU Guest Agent
# From VirtIO ISO or download from: https://www.spice-space.org/download/windows/qemu-ga/
# Install qemu-ga-x86_64.msi

# Enable and start service
Set-Service -Name "QEMU-GA" -StartupType Automatic
Start-Service -Name "QEMU-GA"
```

## Step 7: System Validation and Testing

### 7.1 Comprehensive System Testing
```bash
# On Proxmox host, verify all VMs
qm list

# Check VM status
for vm in 101 102 103 201 202 203 301; do
    echo "VM $vm status:"
    qm status $vm
done

# Check resource usage
pvesh get /nodes/localhost/status
```

### 7.2 Windows Server Functionality Testing
```powershell
# Inside Windows Server VM
# Test core functionality
Get-Service | Where-Object {$_.Status -eq "Running"} | Measure-Object
Test-NetConnection -ComputerName ******* -Port 53
Get-EventLog -LogName System -Newest 10 -EntryType Error

# Test file system access
Get-ChildItem C:\ | Measure-Object
Test-Path "Z:\Hyper-V"  # If Z: drive mounted
```

### 7.3 Performance Validation
```bash
# Monitor VM performance
qm monitor 301

# Check resource allocation
qm config 301

# Verify memory ballooning
qm info 301
```

## Step 8: Final Architecture Optimization

### 8.1 Resource Allocation Optimization
```bash
# Adjust VM resources based on actual usage
# Example: Reduce Windows Server memory if not needed
qm set 301 --memory 12288

# Enable memory ballooning for all VMs
for vm in 101 102 103 201 202 203 301; do
    qm set $vm --balloon 1024
done

# Configure CPU limits if needed
qm set 301 --cpulimit 4
```

### 8.2 Storage Optimization
```bash
# Enable discard for SSD optimization
qm set 301 --scsi0 local-lvm:vm-301-disk-1,discard=on

# Configure backup schedules
pvesh create /cluster/backup \
  --schedule "0 3 * * *" \
  --vmid 101,102,103,201,202,203,301 \
  --storage backup-storage \
  --mode snapshot \
  --compress lzo
```

### 8.3 Network Optimization
```bash
# Configure VLANs if needed
# Example: Separate management network
qm set 301 --net1 virtio,bridge=vmbr0,tag=100

# Configure firewall rules
pve-firewall start
```

## Step 9: Final Validation and Documentation

### 9.1 Complete System Validation
- [ ] All 7 VMs (6 migrated + 1 P2V) running successfully
- [ ] Network connectivity verified for all VMs
- [ ] All services functional
- [ ] Performance acceptable
- [ ] Backup schedules operational
- [ ] Monitoring configured

### 9.2 Create Final Documentation
```bash
# Generate system report
pvesh get /cluster/status > /tmp/final-system-status.txt
qm list > /tmp/final-vm-list.txt
pvesm status > /tmp/final-storage-status.txt
```

### 9.3 Update Network Documentation
- Document new VM IP addresses
- Update DNS records if needed
- Update monitoring systems
- Notify users of any changes

## Rollback Procedures

### Emergency Rollback to Physical
1. **Immediate Rollback:**
   - Boot from Windows Server 2025 installation media
   - Restore from system backup if available
   - Reconfigure Hyper-V and restore VMs from exports

2. **Partial Rollback:**
   - Keep Proxmox with migrated VMs
   - Install Windows Server 2025 on separate hardware
   - Migrate specific services back if needed

### VM-Specific Rollback
- Individual VMs can be exported from Proxmox
- Convert back to VHDX format if needed
- Import to Hyper-V environment

## Success Criteria

### Technical Success
- [ ] All VMs operational on Proxmox
- [ ] Windows Server 2025 running as VM
- [ ] No nested virtualization
- [ ] Performance maintained or improved
- [ ] All services functional

### Operational Success
- [ ] Simplified management through Proxmox
- [ ] Automated backup procedures
- [ ] Improved resource utilization
- [ ] Better monitoring and alerting
- [ ] Reduced hardware dependencies

---

**Phase 2 Status:** Ready for implementation after Phase 1 completion  
**Final Result:** Complete virtualization with Proxmox VE managing all workloads  
**Benefits:** Simplified management, better resource utilization, improved backup/recovery
