# AI Agent Operational Rules

## Command Line Interaction Protocols

### Mandatory Confirmation for Breaking Changes
**ALWAYS confirm before executing commands that:**
- Delete files or directories
- Modify system configurations
- Install or uninstall software packages
- Change file permissions
- Restart services or systems
- Modify database schemas
- Deploy code to production environments

### Breaking Change Identification
**These operations require explicit user approval:**
```bash
# File operations
rm, rmdir, del, Remove-Item
mv, move (when overwriting existing files)
chmod, chown (permission changes)

# System operations  
sudo, su (privilege escalation)
systemctl restart, service restart
reboot, shutdown

# Package management
apt install/remove, yum install/remove
npm install -g, pip install --global
```

### Safe Command Execution
**These commands can be executed without confirmation:**
- File viewing: `cat`, `less`, `head`, `tail`, `ls`, `dir`
- Directory navigation: `cd`, `pwd`
- File creation: `touch`, `echo >` (new files only)
- Status checking: `ps`, `top`, `systemctl status`
- Network testing: `ping`, `curl` (read-only operations)

### Confirmation Format
When breaking changes are needed, present:
1. **What will be changed**: Specific files/systems affected
2. **Why it's necessary**: Business justification
3. **Potential risks**: What could go wrong
4. **Rollback plan**: How to undo if needed

**Example**:
```
⚠️  BREAKING CHANGE CONFIRMATION REQUIRED ⚠️

Action: Delete old log files in /var/log/myapp/
Files affected: 15 files older than 30 days (approximately 2GB)
Reason: Free up disk space (currently at 85% capacity)
Risk: Loss of historical log data for debugging
Rollback: Cannot be undone - logs will be permanently deleted

Proceed? (yes/no):
```

## Security Constraints and Data Handling

### Sensitive Information Protection
**NEVER include in code or logs:**
- API keys, passwords, tokens
- Database connection strings with credentials
- Personal identifiable information (PII)
- Financial data or payment information
- Internal system architecture details

### Environment Variable Usage
**Always use environment variables for:**
- Database credentials
- API keys and secrets
- Third-party service tokens
- Configuration that differs between environments

**Example**:
```python
# ✓ Correct
db_password = os.getenv('DB_PASSWORD')

# ✗ Wrong
db_password = 'mySecretPassword123'
```

### Code Repository Safety
- Never commit sensitive data to version control
- Use `.gitignore` for environment files and secrets
- Scan code for accidentally committed credentials before pushing

## Code Quality Standards

### Code Review Requirements
**Every code change must include:**
1. **Clear purpose**: What problem does this solve?
2. **Testing evidence**: How was this verified to work?
3. **Error handling**: What happens when things go wrong?
4. **Documentation**: Comments for complex logic

### Minimum Quality Checklist
- [ ] Code follows consistent formatting
- [ ] Functions have single, clear purposes
- [ ] Error conditions are handled appropriately
- [ ] No hardcoded values that should be configurable
- [ ] Code includes basic input validation

### Performance Considerations
- Avoid nested loops with large datasets
- Close database connections and file handles
- Use appropriate data structures for the task
- Consider memory usage for long-running processes

## Documentation Requirements

### Mandatory Documentation
**Every project must have:**
- `README.md`: Project purpose, setup instructions, usage examples
- `CHANGELOG.md`: Version history and changes
- Inline comments for complex business logic
- API documentation for any endpoints created

### Documentation Standards
- Use clear, simple language
- Include working code examples
- Document all function parameters and return values
- Explain any non-obvious business rules or constraints

**Example Function Documentation**:
```python
def calculate_shipping_cost(weight, distance, priority=False):
    """
    Calculate shipping cost based on package weight and distance.
    
    Args:
        weight (float): Package weight in pounds
        distance (int): Shipping distance in miles
        priority (bool): Whether to use priority shipping
    
    Returns:
        float: Shipping cost in USD
        
    Raises:
        ValueError: If weight is negative or distance is zero
    """
```

## File Creation and Organization Rules

### PARA Methodology Implementation

#### 0.Inbox/
**Purpose**: Temporary holding for unsorted items
**AI Usage**: Place new files here when unsure of final destination
**Example**: Downloaded templates, quick notes, temporary scripts
**Logic**: Items here need processing and sorting into proper PARA categories

#### 1.Projects/
**Purpose**: Active work with specific deadlines and outcomes
**AI Usage**: Create project folders for time-bound development work
**Structure**: `1.Projects/project-name/src/`, `1.Projects/project-name/docs/`
**Logic**: Has a clear end date and specific deliverable

**Example Placement Decision**:
- ✓ "Build customer portal by Q2" → `1.Projects/customer-portal/`
- ✗ "Maintain server backups" → This is ongoing, belongs in Areas

#### 2.Areas/
**Purpose**: Ongoing responsibilities without end dates
**AI Usage**: Place operational scripts, monitoring tools, maintenance code
**Examples**: Server monitoring, backup scripts, security tools
**Logic**: Continuous responsibility that needs regular attention

#### 3.Resources/
**Purpose**: Reference materials and reusable components
**AI Usage**: Store templates, documentation, learning materials, code libraries
**Examples**: Code snippets, configuration templates, best practices guides
**Logic**: Valuable for future reference but not tied to specific projects

#### 4.Archives/
**Purpose**: Completed or inactive items
**AI Usage**: Move completed projects and outdated resources here
**Logic**: No longer active but kept for historical reference

### File Naming Conventions
- Use lowercase with hyphens: `user-authentication.js`
- Include version numbers for major changes: `api-v2.py`
- Use descriptive names: `customer-data-validator.py` not `validator.py`
- Date format for logs: `YYYY-MM-DD-logfile.txt`

### Folder Structure Logic Explanation
**When AI suggests file placement, must explain reasoning:**

"I'm placing this in `1.Projects/ecommerce-site/` because:
- It's part of active development with a launch deadline
- It's not a general-purpose tool (would be Resources)
- It's not ongoing maintenance (would be Areas)
- It's not completed work (would be Archives)"

## Deployment and Production Rules

### Pre-Deployment Checklist
- [ ] Code tested in staging environment
- [ ] Database migrations tested and reversible
- [ ] Environment variables configured
- [ ] Monitoring and logging in place
- [ ] Rollback plan documented

### Production Change Protocol
1. **Schedule maintenance window** for significant changes
2. **Backup critical data** before modifications
3. **Deploy to staging first** and verify functionality
4. **Monitor system health** after deployment
5. **Document any issues** and resolutions

### Emergency Response
- Keep contact information for critical systems updated
- Document emergency rollback procedures
- Maintain offline access to essential systems
- Test disaster recovery procedures regularly

## Compliance and Best Practices

### Data Handling Compliance
- Follow GDPR principles for EU user data
- Implement proper data retention policies
- Provide data export/deletion capabilities
- Log access to sensitive information

### Regular Maintenance Tasks
- Update dependencies monthly
- Review and rotate access credentials quarterly
- Audit user permissions semi-annually
- Test backup and recovery procedures quarterly

### Communication Standards
- Use clear, non-technical language when explaining issues to stakeholders
- Provide estimated timelines for fixes and implementations
- Document decisions and their reasoning
- Keep stakeholders informed of project status changes
