# Phase 1: VM Migration to Proxmox Implementation Guide

**Project:** Complete Virtualization Migration  
**Phase:** 1 of 2 - VM Migration to Proxmox VE  
**Target:** Migrate 6 Hyper-V VMs to native Proxmox guests  
**Goes in:** 1.Projects - active infrastructure optimization project

## Pre-Migration Status ✅

### File Migration Completed
- ✅ **Total VM files copied:** 214.11 GB
- ✅ **All VM exports completed:** 6 VMs safely backed up
- ✅ **PARA structure created:** Organized on Z: drive
- ✅ **Original files preserved:** D: drive untouched

### VM Inventory Summary
| VM Name | New Name | Type | Size (GB) | Status | Differencing Disk |
|---------|----------|------|-----------|--------|-------------------|
| Debian01 | debian-dev-01 | Development | 6.41 | Ready | No |
| Elara | elara-prod-01 | Production | 116.28 | **Needs Merge** | Yes (.avhdx) |
| Gitlab | gitlab-prod-01 | Production | 22.01 | Ready | No |
| HV-LAN-WS2025 | ws2025-mgmt-01 | Management | 39.56 | **Needs Merge** | Yes (.avhdx) |
| LAN-HV-WS-2025-01 | ws2025-lan-01 | Development | 17.94 | Ready | No |
| rightrhino | rhino-dev-01 | Development | 11.91 | Ready | No |

## Phase 1 Implementation Steps

### Step 1: Proxmox VE Installation

#### 1.1 Hardware Preparation
```bash
# Verify hardware compatibility
# Intel i9-10900: VT-x supported ✅
# 64GB RAM: Sufficient ✅
# Multiple SSDs: Optimal storage ✅
```

#### 1.2 Proxmox Installation Process
1. **Download Proxmox VE 8.x ISO** from https://www.proxmox.com/en/downloads
2. **Create bootable USB** using Rufus or similar tool
3. **Boot from USB** and select installation
4. **Storage Configuration:**
   - **Boot/Root:** Use fastest NVMe SSD (C: drive equivalent)
   - **VM Storage:** Configure remaining drives as separate storage pools
   - **Backup Storage:** Designate one drive for backups

#### 1.3 Initial Proxmox Configuration
```bash
# After installation, access web interface: https://[IP]:8006
# Update system
apt update && apt upgrade -y

# Configure storage pools
pvesm add dir vm-storage --path /mnt/vm-storage --content images
pvesm add dir backup-storage --path /mnt/backup-storage --content backup

# Install additional tools
apt install qemu-img -y
```

### Step 2: Handle Differencing Disks (CRITICAL)

#### 2.1 Merge Elara Differencing Disks
**Location:** `Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\drives\`

```powershell
# On Windows Server (before Proxmox installation)
# Navigate to Elara disk location
cd "Z:\Hyper-V\1_PROJECTS\production-vms\elara-prod-01\drives"

# Identify disk chain
Get-VHD -Path "HV-LAN-WS2025-01_903288FE-967F-45E3-9892-122EFC5C67AE.avhdx"

# Merge differencing disk to parent
Edit-VHD -Path "HV-LAN-WS2025-01_903288FE-967F-45E3-9892-122EFC5C67AE.avhdx" -ParentPath "HV-LAN-WS2025-01.vhdx"

# Verify merge completed
Get-VHD -Path "HV-LAN-WS2025-01.vhdx" | Select-Object Path, VhdType, Size
```

#### 2.2 Merge HV-LAN-WS2025 Differencing Disks
**Location:** `Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\drives\`

```powershell
# Navigate to WS2025 management disk location
cd "Z:\Hyper-V\1_PROJECTS\production-vms\ws2025-mgmt-01\drives"

# Merge differencing disk
Edit-VHD -Path "HV-LAN-WS2025-01_FEC81DD3-AC4C-4229-A17E-45D9339607C8.avhdx" -ParentPath "HV-LAN-WS2025-01.vhdx"

# Verify merge
Get-VHD -Path "HV-LAN-WS2025-01.vhdx" | Select-Object Path, VhdType, Size
```

#### 2.3 Safety Checkpoint
- [ ] Verify merged disks are no longer differencing type
- [ ] Confirm file sizes increased appropriately
- [ ] Test disk integrity with `Get-VHD` command
- [ ] Create additional backup of merged disks

### Step 3: VM Conversion and Import

#### 3.1 Transfer Files to Proxmox
```bash
# Create temporary directory on Proxmox
mkdir -p /tmp/vm-migration

# Transfer VM files from Windows to Proxmox
# Option A: Network share (recommended)
mount -t cifs //AXIOM/Z$ /mnt/windows-share -o username=administrator

# Option B: SCP transfer
scp -r administrator@AXIOM:/Z/Hyper-V/1_PROJECTS/ /tmp/vm-migration/
```

#### 3.2 Convert VHDX to qcow2

##### Development VMs (Start with these for testing)

**Debian01 Conversion:**
```bash
# Navigate to VM files
cd /tmp/vm-migration/development-vms/debian-dev-01/disks/

# Convert VHDX to qcow2
qemu-img convert -p -O qcow2 Debian01.vhdx debian-dev-01-disk0.qcow2

# Verify conversion
qemu-img info debian-dev-01-disk0.qcow2
```

**rightrhino Conversion:**
```bash
cd /tmp/vm-migration/development-vms/rhino-dev-01/Virtual\ Hard\ Disks/

# Convert disk
qemu-img convert -p -O qcow2 rightrhino.vhdx rhino-dev-01-disk0.qcow2

# Verify
qemu-img info rhino-dev-01-disk0.qcow2
```

**ws2025-lan-01 Conversion:**
```bash
cd /tmp/vm-migration/development-vms/ws2025-lan-01/disks/

# Convert disk
qemu-img convert -p -O qcow2 LAN-HV-WS-2025-01.vhdx ws2025-lan-01-disk0.qcow2

# Verify
qemu-img info ws2025-lan-01-disk0.qcow2
```

##### Production VMs (After testing development VMs)

**GitLab Conversion:**
```bash
cd /tmp/vm-migration/production-vms/gitlab-prod-01/Virtual\ Hard\ Disks/

# Convert main disk
qemu-img convert -p -O qcow2 Gitlab.vhdx ********************.qcow2

# Convert Q4OS disk if needed
cd ../Q4OS/Virtual\ Hard\ Disks/
qemu-img convert -p -O qcow2 Q4OS.vhdx gitlab-prod-01-disk1.qcow2
```

**Elara Conversion (after merge):**
```bash
cd /tmp/vm-migration/production-vms/elara-prod-01/drives/

# Convert merged disk
qemu-img convert -p -O qcow2 HV-LAN-WS2025-01.vhdx elara-prod-01-disk0.qcow2

# This will take significant time due to 116GB size
```

**ws2025-mgmt-01 Conversion (after merge):**
```bash
cd /tmp/vm-migration/production-vms/ws2025-mgmt-01/drives/

# Convert merged disk
qemu-img convert -p -O qcow2 HV-LAN-WS2025-01.vhdx ws2025-mgmt-01-disk0.qcow2
```

### Step 4: Create VMs in Proxmox

#### 4.1 Development VM Creation

**Create debian-dev-01 (VM ID: 101):**
```bash
# Create VM
qm create 101 --name "debian-dev-01" --memory 2048 --cores 2 --net0 virtio,bridge=vmbr0 --scsihw virtio-scsi-single --ostype l26

# Import disk
qm disk import 101 /tmp/vm-migration/development-vms/debian-dev-01/disks/debian-dev-01-disk0.qcow2 local-lvm

# Attach disk
qm set 101 --scsi0 local-lvm:vm-101-disk-0

# Set boot order
qm set 101 --boot order=scsi0

# Enable QEMU guest agent
qm set 101 --agent enabled=1
```

**Create rhino-dev-01 (VM ID: 102):**
```bash
qm create 102 --name "rhino-dev-01" --memory 16384 --cores 10 --net0 virtio,bridge=vmbr0 --scsihw virtio-scsi-single --ostype l26
qm disk import 102 /tmp/vm-migration/development-vms/rhino-dev-01/rhino-dev-01-disk0.qcow2 local-lvm
qm set 102 --scsi0 local-lvm:vm-102-disk-0
qm set 102 --boot order=scsi0
qm set 102 --agent enabled=1
```

**Create ws2025-lan-01 (VM ID: 103):**
```bash
qm create 103 --name "ws2025-lan-01" --memory 4096 --cores 10 --net0 virtio,bridge=vmbr0 --scsihw virtio-scsi-single --ostype win11
qm disk import 103 /tmp/vm-migration/development-vms/ws2025-lan-01/ws2025-lan-01-disk0.qcow2 local-lvm
qm set 103 --scsi0 local-lvm:vm-103-disk-0
qm set 103 --boot order=scsi0
qm set 103 --agent enabled=1
```

#### 4.2 Production VM Creation

**Create gitlab-prod-01 (VM ID: 201):**
```bash
qm create 201 --name "gitlab-prod-01" --memory 8192 --cores 10 --net0 virtio,bridge=vmbr0 --scsihw virtio-scsi-single --ostype l26
qm disk import 201 /tmp/vm-migration/production-vms/gitlab-prod-01/********************.qcow2 local-lvm
qm set 201 --scsi0 local-lvm:vm-201-disk-0
qm set 201 --boot order=scsi0
qm set 201 --agent enabled=1
```

**Create elara-prod-01 (VM ID: 202):**
```bash
qm create 202 --name "elara-prod-01" --memory 8192 --cores 10 --net0 virtio,bridge=vmbr0 --scsihw virtio-scsi-single --ostype win11
qm disk import 202 /tmp/vm-migration/production-vms/elara-prod-01/elara-prod-01-disk0.qcow2 local-lvm
qm set 202 --scsi0 local-lvm:vm-202-disk-0
qm set 202 --boot order=scsi0
qm set 202 --agent enabled=1
```

**Create ws2025-mgmt-01 (VM ID: 203):**
```bash
qm create 203 --name "ws2025-mgmt-01" --memory 8192 --cores 10 --net0 virtio,bridge=vmbr0 --scsihw virtio-scsi-single --ostype win11
qm disk import 203 /tmp/vm-migration/production-vms/ws2025-mgmt-01/ws2025-mgmt-01-disk0.qcow2 local-lvm
qm set 203 --scsi0 local-lvm:vm-203-disk-0
qm set 203 --boot order=scsi0
qm set 203 --agent enabled=1
```

### Step 5: Initial VM Testing

#### 5.1 Test Development VMs First
```bash
# Start debian-dev-01
qm start 101

# Monitor boot process
qm monitor 101

# Check status
qm status 101

# If boot issues, try IDE instead of SCSI
qm set 101 --ide0 local-lvm:vm-101-disk-0
qm set 101 --delete scsi0
```

#### 5.2 Boot Issue Troubleshooting
**For Windows VMs that won't boot:**
```bash
# Change to IDE for initial boot
qm set 103 --ide0 local-lvm:vm-103-disk-0
qm set 103 --delete scsi0
qm set 103 --boot order=ide0

# Start VM
qm start 103
```

### Step 6: VirtIO Driver Installation

#### 6.1 Download VirtIO Drivers
```bash
# Download VirtIO ISO
wget https://fedorapeople.org/groups/virt/virtio-win/direct-downloads/stable-virtio/virtio-win.iso -O /var/lib/vz/template/iso/virtio-win.iso
```

#### 6.2 Install VirtIO Drivers in Windows VMs
1. **Attach VirtIO ISO to Windows VMs:**
```bash
qm set 103 --ide2 local:iso/virtio-win.iso,media=cdrom
```

2. **Boot Windows VM and install drivers:**
   - Boot VM with IDE disk
   - Install VirtIO drivers from mounted ISO
   - Shutdown VM
   - Switch back to SCSI with VirtIO

3. **Switch to VirtIO SCSI:**
```bash
qm set 103 --scsi0 local-lvm:vm-103-disk-0
qm set 103 --delete ide0
qm set 103 --boot order=scsi0
```

### Step 7: Network Configuration

#### 7.1 Configure VM Network Settings
- **Linux VMs:** Update network interface names and IP configurations
- **Windows VMs:** Reconfigure network adapters with new MAC addresses
- **Update DHCP reservations** if using static IP assignments

### Step 8: Validation and Testing

#### 8.1 Comprehensive Testing Checklist
- [ ] All VMs boot successfully
- [ ] Network connectivity verified
- [ ] All services start correctly
- [ ] Application functionality confirmed
- [ ] Performance comparable to original
- [ ] QEMU guest agent installed and working

#### 8.2 Performance Optimization
```bash
# Enable memory ballooning
qm set 101 --balloon 1024

# Configure CPU type for better performance
qm set 101 --cpu x86-64-v2-AES

# Enable IO threads for better disk performance
qm set 101 --scsi0 local-lvm:vm-101-disk-0,iothread=1
```

### Step 9: Backup Configuration

#### 9.1 Configure Proxmox Backup
```bash
# Create backup schedule
pvesh create /cluster/backup --schedule "0 2 * * *" --vmid 101,102,103,201,202,203 --storage backup-storage --mode snapshot --compress lzo
```

## Safety Checkpoints

### Checkpoint 1: After Differencing Disk Merge
- [ ] Verify merged disks are no longer differencing type
- [ ] Confirm no data corruption
- [ ] Test original VMs still boot from merged disks

### Checkpoint 2: After First VM Conversion
- [ ] Test conversion process with debian-dev-01
- [ ] Verify VM boots and functions correctly
- [ ] Confirm network connectivity

### Checkpoint 3: After All VM Migrations
- [ ] All 6 VMs operational in Proxmox
- [ ] Network configurations updated
- [ ] All services functional
- [ ] Performance acceptable

## Rollback Procedures

### Individual VM Rollback
1. Stop problematic VM in Proxmox
2. Delete VM configuration
3. Restore from Hyper-V export in `Z:\Hyper-V\4_ARCHIVES\original-exports\`
4. Import back to Hyper-V if needed

### Complete Rollback
1. Stop all Proxmox VMs
2. Reinstall Windows Server 2025 on physical hardware
3. Restore all VMs from exports
4. Reconfigure Hyper-V environment

---

**Phase 1 Status:** Ready for implementation  
**Next Phase:** P2V migration of Windows Server 2025 host  
**Critical Success Factors:** Proper differencing disk merge, thorough testing
